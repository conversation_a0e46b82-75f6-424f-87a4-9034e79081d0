#!/usr/bin/env python3
"""
Script de test pour valider les améliorations des providers.

Ce script teste:
1. La gestion robuste du streaming
2. La retry logic avec gestion d'erreurs
3. La détection de fin de stream
4. Les fallbacks en cas d'erreur
"""

import sys
import os
import time
import traceback
from typing import Dict, Any

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.core.openai_client import OpenAIClient, create_openai_client
from scripts.core.groq_client import GroqClient, create_groq_client
from scripts.core.ollama_client import OllamaClient
from scripts.core.huggingface_client import HuggingFaceClient
from scripts.core.constants import DefaultConfigurations


def test_streaming_robustness(client, client_name: str, test_prompt: str) -> Dict[str, Any]:
    """Teste la robustesse du streaming pour un client donné."""
    print(f"\n{'='*60}")
    print(f"🧪 Test streaming robustesse - {client_name}")
    print(f"{'='*60}")
    
    results = {
        'client_name': client_name,
        'success': False,
        'response_length': 0,
        'chunks_received': 0,
        'error': None,
        'execution_time': 0
    }
    
    start_time = time.time()
    
    try:
        if hasattr(client, 'chat_completion'):
            # Format chat pour OpenAI/Groq
            messages = [
                {"role": "system", "content": "Tu es un assistant IA expert en résolution de puzzles logiques."},
                {"role": "user", "content": test_prompt}
            ]
            response = client.chat_completion(messages)
        else:
            # Format simple pour Ollama/HuggingFace
            response = client.generate_response(test_prompt)
        
        results['success'] = True
        results['response_length'] = len(response) if response else 0
        results['execution_time'] = time.time() - start_time
        
        print(f"✅ Test réussi!")
        print(f"📝 Longueur réponse: {results['response_length']} caractères")
        print(f"⏱️ Temps d'exécution: {results['execution_time']:.2f}s")
        
        # Afficher un extrait de la réponse
        if response:
            preview = response[:200] + "..." if len(response) > 200 else response
            print(f"🔍 Aperçu: {preview}")
        
    except Exception as e:
        results['error'] = str(e)
        results['execution_time'] = time.time() - start_time
        
        print(f"❌ Test échoué: {e}")
        print(f"⏱️ Temps avant échec: {results['execution_time']:.2f}s")
        
        # Afficher la stack trace pour debug
        print(f"🔍 Stack trace:")
        traceback.print_exc()
    
    return results


def test_retry_logic(client, client_name: str) -> Dict[str, Any]:
    """Teste la logique de retry en simulant des erreurs."""
    print(f"\n{'='*60}")
    print(f"🔄 Test retry logic - {client_name}")
    print(f"{'='*60}")
    
    results = {
        'client_name': client_name,
        'retry_available': False,
        'error': None
    }
    
    try:
        # Vérifier si le client a la méthode _execute_with_retry
        if hasattr(client, '_execute_with_retry'):
            results['retry_available'] = True
            print(f"✅ Retry logic disponible")
            
            # Test simple de la méthode retry
            def test_operation():
                return "Test réussi"
            
            result = client._execute_with_retry(test_operation, max_retries=1, operation_name="test")
            if result == "Test réussi":
                print(f"✅ Méthode retry fonctionnelle")
            else:
                print(f"⚠️ Méthode retry retourne un résultat inattendu: {result}")
        else:
            print(f"❌ Retry logic non disponible")
            
    except Exception as e:
        results['error'] = str(e)
        print(f"❌ Erreur lors du test retry: {e}")
    
    return results


def main():
    """Fonction principale de test."""
    print("🚀 Démarrage des tests d'amélioration des providers")
    print("=" * 80)
    
    # Prompt de test qui devrait générer une réponse substantielle
    test_prompt = """
    Analyse ce puzzle logique étape par étape:
    
    Grille d'entrée 3x3:
    1 2 3
    4 5 6
    7 8 9
    
    Règle: Chaque nombre doit être transformé selon la formule: nouveau_nombre = (ancien_nombre * 2) + 1
    
    Calcule la grille de sortie et explique ton raisonnement en détail.
    """
    
    all_results = []
    
    # Test OpenAI/OpenRouter
    try:
        print(f"\n🔧 Initialisation client OpenAI...")
        openai_client = create_openai_client("openrouter")
        
        # Test streaming
        streaming_result = test_streaming_robustness(openai_client, "OpenAI/OpenRouter", test_prompt)
        all_results.append(streaming_result)
        
        # Test retry logic
        retry_result = test_retry_logic(openai_client, "OpenAI/OpenRouter")
        all_results.append(retry_result)
        
    except Exception as e:
        print(f"❌ Erreur initialisation OpenAI: {e}")
        all_results.append({
            'client_name': 'OpenAI/OpenRouter',
            'success': False,
            'error': f"Initialisation échouée: {e}"
        })
    
    # Test Groq
    try:
        print(f"\n🔧 Initialisation client Groq...")
        groq_client = create_groq_client("groq")
        
        # Test streaming
        streaming_result = test_streaming_robustness(groq_client, "Groq", test_prompt)
        all_results.append(streaming_result)
        
        # Test retry logic
        retry_result = test_retry_logic(groq_client, "Groq")
        all_results.append(retry_result)
        
    except Exception as e:
        print(f"❌ Erreur initialisation Groq: {e}")
        all_results.append({
            'client_name': 'Groq',
            'success': False,
            'error': f"Initialisation échouée: {e}"
        })
    
    # Test Ollama
    try:
        print(f"\n🔧 Initialisation client Ollama...")
        ollama_client = OllamaClient()
        
        # Test streaming
        streaming_result = test_streaming_robustness(ollama_client, "Ollama", test_prompt)
        all_results.append(streaming_result)
        
        # Test retry logic
        retry_result = test_retry_logic(ollama_client, "Ollama")
        all_results.append(retry_result)
        
    except Exception as e:
        print(f"❌ Erreur initialisation Ollama: {e}")
        all_results.append({
            'client_name': 'Ollama',
            'success': False,
            'error': f"Initialisation échouée: {e}"
        })
    
    # Test HuggingFace
    try:
        print(f"\n🔧 Initialisation client HuggingFace...")
        hf_client = HuggingFaceClient()
        
        # Test streaming (pas de streaming pour HF, mais test de robustesse)
        streaming_result = test_streaming_robustness(hf_client, "HuggingFace", test_prompt)
        all_results.append(streaming_result)
        
        # Test retry logic
        retry_result = test_retry_logic(hf_client, "HuggingFace")
        all_results.append(retry_result)
        
    except Exception as e:
        print(f"❌ Erreur initialisation HuggingFace: {e}")
        all_results.append({
            'client_name': 'HuggingFace',
            'success': False,
            'error': f"Initialisation échouée: {e}"
        })
    
    # Résumé des résultats
    print(f"\n{'='*80}")
    print(f"📊 RÉSUMÉ DES TESTS")
    print(f"{'='*80}")
    
    successful_tests = 0
    total_tests = len(all_results)
    
    for result in all_results:
        client_name = result.get('client_name', 'Inconnu')
        success = result.get('success', False)
        error = result.get('error')
        
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{status} - {client_name}")
        
        if success:
            successful_tests += 1
            response_length = result.get('response_length', 0)
            execution_time = result.get('execution_time', 0)
            print(f"  📝 Réponse: {response_length} chars, ⏱️ Temps: {execution_time:.2f}s")
        else:
            print(f"  ❌ Erreur: {error}")
        
        print()
    
    success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"🎯 Taux de succès global: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        print(f"🎉 Excellents résultats! Les améliorations fonctionnent bien.")
    elif success_rate >= 50:
        print(f"⚠️ Résultats mitigés. Certaines améliorations nécessitent des ajustements.")
    else:
        print(f"🚨 Résultats préoccupants. Les améliorations nécessitent une révision.")
    
    return all_results


if __name__ == "__main__":
    try:
        results = main()
        print(f"\n✅ Tests terminés.")
    except KeyboardInterrupt:
        print(f"\n⚠️ Tests interrompus par l'utilisateur.")
    except Exception as e:
        print(f"\n❌ Erreur fatale lors des tests: {e}")
        traceback.print_exc()
