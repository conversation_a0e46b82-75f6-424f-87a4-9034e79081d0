#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script principal pour le système de prompts ARC AGI améliorés
Point d'entrée unique pour toutes les fonctionnalités
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# Forcer l'encodage UTF-8 pour la sortie
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Ajouter le répertoire scripts au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'scripts'))

# Imports pour l'analyse automatisée
try:
    from scripts.core.automated_analyzer import AutomatedARCAnalyzer
    from scripts.core.constants import ExitCodes
    from scripts.core.exceptions import (
        PuzzleNotFoundError, ChatSessionError, ResponseParsingError,
        SolutionValidationError, LearningSystemError
    )
except ImportError as e:
    print(f"⚠️  Erreur d'import: {e}")
    print("Certaines fonctionnalités peuvent ne pas être disponibles.")
    AutomatedARCAnalyzer = None
    ExitCodes = None

def main():
    parser = argparse.ArgumentParser(
        description='Système de prompts ARC AGI améliorés',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Commandes disponibles:
  generate    Générer un prompt amélioré pour un puzzle
  analyze     Analyse automatisée complète d'un puzzle
  validate    Valider la généricité des insights
  test        Exécuter les tests du système
  cache       Gestion du cache d'analyses
  
Exemples:
  python arc_enhanced_prompt.py generate --taskid 2204b7a8
  python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8 --enable-learning
  python arc_enhanced_prompt.py validate
  python arc_enhanced_prompt.py test
  python arc_enhanced_prompt.py cache stats
  python arc_enhanced_prompt.py cache cleanup --max-age-days 7
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Commandes disponibles')
    
    # Commande generate
    generate_parser = subparsers.add_parser('generate', help='Générer un prompt amélioré')
    generate_parser.add_argument('--taskid', type=str, required=True, help='ID du puzzle')
    generate_parser.add_argument('--subset', type=str, default='training', 
                                choices=['training', 'evaluation'], help='Sous-ensemble')
    generate_parser.add_argument('--show-insights-only', action='store_true',
                                help='Afficher seulement les insights')
    generate_parser.add_argument('--force-regenerate', action='store_true',
                                help='Forcer la régénération de l\'analyse')
    generate_parser.add_argument('--quiet', action='store_true', help='Mode silencieux')
    
    # Commande analyze - Nouvelle commande d'analyse automatisée
    analyze_parser = subparsers.add_parser('analyze', help='Analyse automatisée complète')
    analyze_parser.add_argument('--puzzle-id', type=str, 
                               help='ID du puzzle à analyser')
    analyze_parser.add_argument('--chat-session', type=str, default='blind_analysis',
                               choices=['default', 'blind_analysis', 'learning_extraction'],
                               help='Type de session chat')
    analyze_parser.add_argument('--ai-provider', type=str, default='openrouter',
                               choices=['simulation', 'ollama', 'openai', 'groq', 'openrouter', 'huggingface'],
                               help='Fournisseur d\'IA à utiliser (défaut: openrouter avec DeepSeek R1 gratuit)')
    
    # Options Ollama
    analyze_parser.add_argument('--ollama-model', type=str,
                               help='Modèle Ollama à utiliser (ex: llama3.1:8b)')
    analyze_parser.add_argument('--ollama-url', type=str, default='http://localhost:11434',
                               help='URL de base d\'Ollama')
    
    # Options OpenAI
    analyze_parser.add_argument('--openai-model', type=str,
                               help='Modèle OpenAI à utiliser (ex: gpt-4o-mini)')
    # Options Groq
    analyze_parser.add_argument('--groq-model', type=str,
                               help='Modèle Groq à utiliser (ex: moonshotai/kimi-k2-instruct)')    
    # Options OpenRouter
    analyze_parser.add_argument('--openrouter-model', type=str,
                               help='Modèle OpenRouter à utiliser (ex: deepseek/deepseek-r1-0528:free)')
    
    # Options Hugging Face
    analyze_parser.add_argument('--huggingface-model', type=str,
                               help='Modèle Hugging Face à utiliser')
    analyze_parser.add_argument('--save-success', action='store_true', default=True,
                               help='Sauvegarder les analyses réussies')
    analyze_parser.add_argument('--enable-learning', action='store_true', default=False,
                               help='Activer l\'apprentissage automatique')
    analyze_parser.add_argument('--arc-data-dir', type=str, default='../arc-puzzle/arcdata',
                               help='Répertoire des données ARC')
    analyze_parser.add_argument('--analysis-dir', type=str, default='analysis_data',
                               help='Répertoire des analyses')
    analyze_parser.add_argument('--results-dir', type=str, default='arc_results',
                               help='Répertoire des résultats')
    analyze_parser.add_argument('--quiet', action='store_true',
                               help='Mode silencieux (pas d\'indicateurs de progression)')
    analyze_parser.add_argument('--log-level', type=str, default='INFO',
                               choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                               help='Niveau de logging')
    analyze_parser.add_argument('--show-examples', action='store_true',
                               help='Afficher des exemples d\'utilisation et quitter')
    
    # Commande test-ai - Tester les connexions IA
    test_ai_parser = subparsers.add_parser('test-ai', help='Tester les connexions IA')
    test_ai_parser.add_argument('--provider', type=str, default='all',
                               choices=['all', 'ollama', 'openai', 'openrouter', 'huggingface'],
                               help='Provider à tester')
    test_ai_parser.add_argument('--model', type=str,
                               help='Tester un modèle spécifique')
    test_ai_parser.add_argument('--list-models', action='store_true',
                               help='Lister tous les modèles disponibles')
    test_ai_parser.add_argument('--ollama-url', type=str, default='http://localhost:11434',
                               help='URL de base d\'Ollama')
    
    # Commande env-status - Vérifier les variables d'environnement
    env_parser = subparsers.add_parser('env-status', help='Vérifier les clés API et configuration')
    
    # Commande test-deepseek - Tester uniquement DeepSeek R1
    deepseek_parser = subparsers.add_parser('test-deepseek', help='Tester DeepSeek R1 avec un prompt simple')
    deepseek_parser.add_argument('--prompt', type=str, default='Dis bonjour en français.',
                                help='Prompt de test à envoyer')
    
    # Commande validate
    validate_parser = subparsers.add_parser('validate', help='Valider la généricité')
    
    # Commande test
    test_parser = subparsers.add_parser('test', help='Exécuter les tests')
    test_parser.add_argument('--complete', action='store_true', 
                            help='Exécuter tous les tests')
    
    # Commande cache - Nouvelle commande de gestion du cache
    cache_parser = subparsers.add_parser('cache', help='Gestion du cache d\'analyses')
    cache_subparsers = cache_parser.add_subparsers(dest='cache_action', help='Actions de cache')
    
    # Sous-commande cache stats
    cache_stats_parser = cache_subparsers.add_parser('stats', help='Afficher les statistiques du cache')
    cache_stats_parser.add_argument('--analysis-dir', type=str, default='analysis_data',
                                   help='Répertoire des analyses')
    
    # Sous-commande cache cleanup
    cache_cleanup_parser = cache_subparsers.add_parser('cleanup', help='Nettoyer les analyses obsolètes')
    cache_cleanup_parser.add_argument('--analysis-dir', type=str, default='analysis_data',
                                     help='Répertoire des analyses')
    cache_cleanup_parser.add_argument('--max-age-days', type=int, default=30,
                                     help='Âge maximum en jours avant suppression')
    cache_cleanup_parser.add_argument('--dry-run', action='store_true',
                                     help='Simulation sans suppression réelle')
    
    # Sous-commande cache preload
    cache_preload_parser = cache_subparsers.add_parser('preload', help='Précharger des analyses')
    cache_preload_parser.add_argument('--puzzle-ids', type=str, nargs='+', required=True,
                                     help='IDs des puzzles à précharger')
    cache_preload_parser.add_argument('--subset', type=str, default='training',
                                     choices=['training', 'evaluation'], help='Sous-ensemble')
    cache_preload_parser.add_argument('--analysis-dir', type=str, default='analysis_data',
                                     help='Répertoire des analyses')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Exécuter la commande appropriée
    if args.command == 'generate':
        return run_generate(args)
    elif args.command == 'analyze':
        return run_analyze(args)
    elif args.command == 'test-ai':
        return run_test_ai(args)
    elif args.command == 'env-status':
        return run_env_status(args)
    elif args.command == 'test-deepseek':
        return run_test_deepseek(args)
    elif args.command == 'validate':
        return run_validate()
    elif args.command == 'test':
        return run_test(args)
    elif args.command == 'cache':
        return run_cache(args)
    
    return 0

def run_generate(args):
    """Exécuter la génération de prompt"""
    cmd = ['python', 'scripts/core/generate_enhanced_prompt.py']
    
    cmd.extend(['--taskid', args.taskid])
    cmd.extend(['--subset', args.subset])
    
    # Ajuster les chemins pour qu'ils soient relatifs à la racine
    cmd.extend(['--arc-data-dir', '../arc-puzzle/arcdata/'])
    cmd.extend(['--analysis-dir', 'analysis_data'])
    cmd.extend(['--output-dir', 'arc_results'])
    
    if args.show_insights_only:
        cmd.append('--show-insights-only')
    if args.force_regenerate:
        cmd.append('--force-regenerate')
    if args.quiet:
        cmd.append('--quiet')
    
    try:
        return subprocess.run(cmd, check=True).returncode
    except subprocess.CalledProcessError as e:
        return e.returncode

def run_validate():
    """Exécuter la validation"""
    cmd = ['python', 'scripts/utils/validate_generic_insights.py']
    
    try:
        return subprocess.run(cmd, check=True).returncode
    except subprocess.CalledProcessError as e:
        return e.returncode

def run_test_ai(args):
    """Tester les connexions IA"""
    providers_to_test = []
    
    if args.provider == 'all':
        providers_to_test = ['ollama', 'openai', 'openrouter', 'huggingface']
    else:
        providers_to_test = [args.provider]
    
    print("🤖 Test des connexions IA")
    print("=" * 50)
    
    results = {}
    
    for provider in providers_to_test:
        print(f"\n🔍 Test de {provider.upper()}...")
        print("-" * 30)
        
        try:
            if provider == 'ollama':
                results[provider] = _test_ollama(args)
            elif provider == 'openai':
                results[provider] = _test_openai(args)
            elif provider == 'openrouter':
                results[provider] = _test_openrouter(args)
            elif provider == 'huggingface':
                results[provider] = _test_huggingface(args)
                
        except Exception as e:
            print(f"❌ Erreur lors du test de {provider}: {e}")
            results[provider] = False
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    success_count = 0
    for provider, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {provider.upper()}: {'Disponible' if success else 'Non disponible'}")
        if success:
            success_count += 1
    
    print(f"\n🎯 {success_count}/{len(results)} providers disponibles")
    
    return 0 if success_count > 0 else 1

def _test_ollama(args):
    """Test spécifique pour Ollama"""
    try:
        from scripts.core.ollama_client import create_ollama_client, OllamaError
        
        client = create_ollama_client(model=args.model, base_url=args.ollama_url)
        test_result = client.test_connection()
        
        if test_result['available']:
            print(f"✅ Ollama accessible ({len(test_result['models'])} modèles)")
            if args.list_models:
                for model in test_result['models']:
                    marker = "🎯" if model == test_result['current_model'] else "  "
                    print(f"   {marker} {model}")
            return True
        else:
            print(f"❌ Ollama non accessible: {test_result.get('error', 'Raison inconnue')}")
            return False
            
    except ImportError:
        print("❌ Module Ollama non disponible")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def _test_openai(args):
    """Test spécifique pour OpenAI"""
    try:
        from scripts.core.openai_client import create_openai_client, OpenAIError
        
        client = create_openai_client('openai', model=args.model)
        test_result = client.test_connection()
        
        if test_result['available']:
            print(f"✅ OpenAI accessible (modèle: {test_result['current_model']})")
            return True
        else:
            print(f"❌ OpenAI non accessible: {test_result.get('error', 'Raison inconnue')}")
            return False
            
    except ImportError:
        print("❌ Module OpenAI non disponible")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def _test_openrouter(args):
    """Test spécifique pour OpenRouter"""
    try:
        from scripts.core.openai_client import create_openai_client, OpenAIError
        
        client = create_openai_client('openrouter', model=args.model)
        test_result = client.test_connection()
        
        if test_result['available']:
            print(f"✅ OpenRouter accessible (modèle: {test_result['current_model']})")
            return True
        else:
            print(f"❌ OpenRouter non accessible: {test_result.get('error', 'Raison inconnue')}")
            return False
            
    except ImportError:
        print("❌ Module OpenRouter non disponible")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def _test_huggingface(args):
    """Test spécifique pour Hugging Face"""
    try:
        from scripts.core.huggingface_client import create_huggingface_client, HuggingFaceError
        
        client = create_huggingface_client(model=args.model)
        test_result = client.test_connection()
        
        if test_result['available']:
            print(f"✅ Hugging Face accessible (modèle: {test_result['current_model']})")
            return True
        else:
            print(f"❌ Hugging Face non accessible: {test_result.get('error', 'Raison inconnue')}")
            return False
            
    except ImportError:
        print("❌ Module Hugging Face non disponible")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def run_analyze(args):
    """Exécuter l'analyse automatisée complète"""
    # Afficher les exemples si demandé
    if hasattr(args, 'show_examples') and args.show_examples:
        _display_analyze_examples()
        return 0
    
    # Vérifier que puzzle-id est fourni si on ne montre pas les exemples
    if not args.puzzle_id:
        print("❌ Erreur: --puzzle-id est requis")
        print("💡 Utilisez --show-examples pour voir des exemples d'utilisation")
        print("💡 Utilisez --help pour voir toutes les options")
        return ExitCodes.CONFIGURATION_ERROR if ExitCodes else 8
    
    if AutomatedARCAnalyzer is None:
        print("❌ Erreur: AutomatedARCAnalyzer non disponible")
        print("💡 Solution: Vérifiez que tous les composants sont installés correctement.")
        print("   - Assurez-vous que scripts/core/automated_analyzer.py existe")
        print("   - Vérifiez que toutes les dépendances sont installées")
        return ExitCodes.CONFIGURATION_ERROR if ExitCodes else 8
    
    try:
        # Validation complète des paramètres d'entrée
        validation_errors = _validate_analyze_parameters(args)
        if validation_errors:
            print("❌ Erreurs de validation des paramètres:")
            for error in validation_errors:
                print(f"   • {error}")
            print("\n💡 Utilisez --help pour voir les options disponibles")
            return ExitCodes.CONFIGURATION_ERROR if ExitCodes else 8
        
        # Configuration de l'analyseur
        config = {
            'arc_data_dir': args.arc_data_dir,
            'analysis_dir': args.analysis_dir,
            'results_dir': args.results_dir,
            'chat_session_type': args.chat_session,
            'ai_provider': args.ai_provider,
            'ollama_model': args.ollama_model,
            'ollama_base_url': args.ollama_url,
            'openai_model': args.openai_model,
            'openrouter_model': args.openrouter_model,
            'huggingface_model': args.huggingface_model,
            'enable_learning': args.enable_learning,
            'save_success': args.save_success,
            'show_progress': not args.quiet,
            'log_level': args.log_level
        }
        
        # Créer et configurer l'analyseur
        analyzer = AutomatedARCAnalyzer(config)
        
        # Options d'analyse
        analysis_options = {
            'force_regenerate': False,  # Peut être ajouté comme paramètre plus tard
            'validate_insights': True
        }
        
        # Exécuter l'analyse
        if not args.quiet:
            print(f"🚀 Démarrage de l'analyse automatisée pour le puzzle {args.puzzle_id}")
            print(f"📂 Données ARC: {args.arc_data_dir}")
            print(f"💬 Session chat: {args.chat_session}")
            print(f"🤖 Fournisseur IA: {args.ai_provider}")
            if args.ai_provider == 'ollama':
                model = args.ollama_model or 'qwen3-arc:latest (défaut)'
                print(f"🦙 Modèle Ollama: {model}")
                print(f"🌐 URL Ollama: {args.ollama_url}")
            elif args.ai_provider == 'openai':
                model = args.openai_model or 'gpt-4o-mini (défaut)'
                print(f"🧠 Modèle OpenAI: {model}")
            elif args.ai_provider == 'groq':
                model = args.groq_model or 'groq (défaut)'
                print(f"🦙 Modèle Groq: {model}")
            elif args.ai_provider == 'openrouter':
                from scripts.core.env_loader import get_default_model
                default_model = get_default_model('openrouter') or 'meta-llama/llama-3.2-3b-instruct:free'
                model = args.openrouter_model or f'{default_model} (défaut)'
                print(f"🌐 Modèle OpenRouter: {model}")
                if 'deepseek-r1' in model:
                    print("⏳ DeepSeek R1 est un modèle de raisonnement - peut prendre 5-10 minutes")
                elif 'qwen3-30b' in model or '30b' in model:
                    print("⏳ Modèle 30B - peut prendre 2-5 minutes pour réfléchir")
                elif 'qwen' in model and 'free' in model:
                    print("⏳ Modèle Qwen gratuit - peut prendre 1-3 minutes")
            elif args.ai_provider == 'huggingface':
                model = args.huggingface_model or 'microsoft/DialoGPT-large (défaut)'
                print(f"🤗 Modèle Hugging Face: {model}")
            print(f"🧠 Apprentissage: {'activé' if args.enable_learning else 'désactivé'}")
            print("-" * 60)
        
        result = analyzer.analyze_puzzle(args.puzzle_id, analysis_options)
        
        # Afficher le résumé final avec informations détaillées
        if not args.quiet:
            _display_analysis_summary(result, args)
        
        # Retourner le code de sortie approprié
        if result.success:
            return ExitCodes.SUCCESS if ExitCodes else 0
        else:
            # Déterminer le code d'erreur spécifique basé sur le message d'erreur
            if result.error_message:
                error_msg = result.error_message.lower()
                if "puzzle" in error_msg and "not found" in error_msg:
                    return ExitCodes.PUZZLE_NOT_FOUND if ExitCodes else 1
                elif "chat" in error_msg or "session" in error_msg:
                    return ExitCodes.CHAT_SESSION_ERROR if ExitCodes else 3
                elif "parsing" in error_msg or "parse" in error_msg:
                    return ExitCodes.PARSING_ERROR if ExitCodes else 4
                elif "validation" in error_msg or "validate" in error_msg:
                    return ExitCodes.VALIDATION_ERROR if ExitCodes else 5
                elif "learning" in error_msg:
                    return ExitCodes.LEARNING_ERROR if ExitCodes else 6
            
            return ExitCodes.UNKNOWN_ERROR if ExitCodes else 99
    
    except Exception as e:
        # Gestion des exceptions spécifiques avec messages d'aide
        if ExitCodes:
            if isinstance(e, PuzzleNotFoundError):
                _display_helpful_error_message('puzzle_not_found', str(e))
                return ExitCodes.PUZZLE_NOT_FOUND
            elif isinstance(e, ChatSessionError):
                _display_helpful_error_message('chat_session_error', str(e))
                return ExitCodes.CHAT_SESSION_ERROR
            elif isinstance(e, ResponseParsingError):
                _display_helpful_error_message('parsing_error', str(e))
                return ExitCodes.PARSING_ERROR
            elif isinstance(e, SolutionValidationError):
                _display_helpful_error_message('validation_error', str(e))
                return ExitCodes.VALIDATION_ERROR
            elif isinstance(e, LearningSystemError):
                _display_helpful_error_message('learning_error', str(e))
                return ExitCodes.LEARNING_ERROR
        
        # Erreur générique avec aide contextuelle
        print(f"\n❌ Erreur inattendue: {e}")
        print("\n💡 Suggestions de dépannage:")
        print("   • Vérifiez que tous les composants sont installés")
        print("   • Essayez avec --log-level DEBUG pour plus d'informations")
        print("   • Consultez la documentation pour les prérequis")
        print("   • Vérifiez les permissions des fichiers et répertoires")
        
        if not args.quiet:
            print("\n🔍 Trace détaillée de l'erreur:")
            import traceback
            traceback.print_exc()
        
        return ExitCodes.UNKNOWN_ERROR if ExitCodes else 99

def _validate_analyze_parameters(args):
    """
    Valide les paramètres de la commande analyze.
    
    Args:
        args: Arguments parsés de la ligne de commande
        
    Returns:
        List[str]: Liste des erreurs de validation (vide si tout est valide)
    """
    errors = []
    
    # Validation de l'ID du puzzle
    if not args.puzzle_id or not args.puzzle_id.strip():
        errors.append("ID de puzzle requis et non vide")
    elif len(args.puzzle_id.strip()) < 8:
        errors.append("ID de puzzle trop court (minimum 8 caractères)")
    elif not args.puzzle_id.replace('-', '').replace('_', '').isalnum():
        errors.append("ID de puzzle doit contenir uniquement des caractères alphanumériques, tirets et underscores")
    
    # Validation des répertoires
    arc_data_path = Path(args.arc_data_dir)
    if not arc_data_path.exists():
        errors.append(f"Répertoire de données ARC non trouvé: {arc_data_path}")
        errors.append("💡 Vérifiez le paramètre --arc-data-dir ou créez le répertoire")
    
    # Validation des répertoires de sortie (créer s'ils n'existent pas)
    try:
        Path(args.analysis_dir).mkdir(parents=True, exist_ok=True)
        Path(args.results_dir).mkdir(parents=True, exist_ok=True)
    except PermissionError:
        errors.append(f"Permissions insuffisantes pour créer les répertoires de sortie")
    except Exception as e:
        errors.append(f"Erreur lors de la création des répertoires: {e}")
    
    # Validation du niveau de logging
    valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
    if args.log_level not in valid_log_levels:
        errors.append(f"Niveau de logging invalide: {args.log_level}. Valeurs acceptées: {', '.join(valid_log_levels)}")
    
    # Validation du type de session chat
    valid_session_types = ['default', 'blind_analysis', 'learning_extraction']
    if args.chat_session not in valid_session_types:
        errors.append(f"Type de session chat invalide: {args.chat_session}. Valeurs acceptées: {', '.join(valid_session_types)}")
    
    return errors

def _display_helpful_error_message(error_type, details=None):
    """
    Affiche un message d'erreur utile avec des suggestions de résolution.
    
    Args:
        error_type: Type d'erreur
        details: Détails supplémentaires sur l'erreur
    """
    error_messages = {
        'puzzle_not_found': {
            'title': "Puzzle non trouvé",
            'description': "Le puzzle spécifié n'a pas pu être localisé",
            'solutions': [
                "Vérifiez que l'ID du puzzle est correct",
                "Assurez-vous que le puzzle existe dans le répertoire training ou evaluation",
                "Vérifiez le paramètre --arc-data-dir",
                "Exemple d'ID valide: 2204b7a8"
            ]
        },
        'chat_session_error': {
            'title': "Erreur de session chat",
            'description': "Impossible d'établir ou de maintenir la session chat avec l'IA",
            'solutions': [
                "Vérifiez votre connexion internet",
                "Assurez-vous que les clés API sont configurées",
                "Essayez un autre type de session avec --chat-session",
                "Vérifiez les logs pour plus de détails"
            ]
        },
        'parsing_error': {
            'title': "Erreur de parsing",
            'description': "La réponse de l'IA n'a pas pu être analysée correctement",
            'solutions': [
                "La réponse de l'IA était peut-être malformée",
                "Essayez de relancer l'analyse",
                "Vérifiez les logs en mode DEBUG pour voir la réponse brute",
                "Contactez le support si le problème persiste"
            ]
        },
        'validation_error': {
            'title': "Erreur de validation",
            'description': "Impossible de valider la solution proposée",
            'solutions': [
                "La grille proposée pourrait avoir des dimensions incorrectes",
                "Vérifiez que le puzzle a une solution de référence",
                "Consultez les logs pour plus de détails",
                "Essayez avec un autre puzzle pour tester"
            ]
        },
        'learning_error': {
            'title': "Erreur d'apprentissage",
            'description': "Le système d'apprentissage a rencontré un problème",
            'solutions': [
                "Désactivez l'apprentissage avec --no-enable-learning",
                "Vérifiez que les règles de généricité sont respectées",
                "Consultez les logs pour identifier le problème",
                "L'analyse peut continuer sans apprentissage"
            ]
        }
    }
    
    if error_type in error_messages:
        msg = error_messages[error_type]
        print(f"\n❌ {msg['title']}")
        print(f"📝 {msg['description']}")
        if details:
            print(f"🔍 Détails: {details}")
        print("\n💡 Solutions possibles:")
        for solution in msg['solutions']:
            print(f"   • {solution}")
        print()

def _display_analysis_summary(result, args):
    """
    Affiche un résumé détaillé de l'analyse avec conseils contextuels.
    
    Args:
        result: Résultat de l'analyse
        args: Arguments de la ligne de commande
    """
    print("\n" + "="*70)
    print("📊 RÉSUMÉ FINAL DE L'ANALYSE")
    print("="*70)
    
    # Informations de base
    print(f"🧩 Puzzle: {result.puzzle_id}")
    print(f"⏱️  Durée: {result.execution_time:.2f}s")
    print(f"📅 Timestamp: {result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Statut avec détails
    if result.success:
        print(f"🎉 Statut: ✅ SUCCÈS - Puzzle résolu!")
        
        if result.validation_result:
            accuracy = result.validation_result.accuracy_percentage
            print(f"🎯 Précision: {accuracy:.1f}%")
            
            if accuracy == 100.0:
                print("🏆 Solution parfaite!")
            elif accuracy >= 95.0:
                print("⭐ Excellente solution!")
            elif accuracy >= 80.0:
                print("👍 Bonne solution!")
        
        if result.saved_to:
            print(f"💾 Résultats sauvegardés: {result.saved_to}")
            print("💡 Consultez ce fichier pour voir l'analyse détaillée")
        
        if args.enable_learning:
            print("🧠 Apprentissage activé - Insights extraits pour améliorer les futures analyses")
        
    else:
        print(f"❌ Statut: ÉCHEC - Puzzle non résolu")
        
        if result.validation_result:
            accuracy = result.validation_result.accuracy_percentage
            errors = result.validation_result.total_errors
            print(f"📊 Précision partielle: {accuracy:.1f}%")
            print(f"🔍 Erreurs détectées: {errors}")
            
            if accuracy > 50:
                print("💡 Solution partiellement correcte - Bon début!")
            else:
                print("💡 Solution nécessite des améliorations importantes")
        
        if result.error_message:
            print(f"🚨 Erreur: {result.error_message}")
    
    # Informations sur la configuration utilisée
    print(f"\n⚙️  Configuration utilisée:")
    print(f"   • Session chat: {args.chat_session}")
    print(f"   • Apprentissage: {'activé' if args.enable_learning else 'désactivé'}")
    print(f"   • Sauvegarde: {'activée' if args.save_success else 'désactivée'}")
    print(f"   • Niveau de log: {args.log_level}")
    
    # Conseils pour la suite
    print(f"\n💡 Prochaines étapes suggérées:")
    if result.success:
        print("   • Analysez le fichier de résultats pour comprendre la solution")
        print("   • Essayez d'autres puzzles pour tester la robustesse")
        if not args.enable_learning:
            print("   • Activez --enable-learning pour améliorer les futures analyses")
    else:
        print("   • Consultez les logs avec --log-level DEBUG pour plus de détails")
        print("   • Essayez un autre type de session avec --chat-session")
        print("   • Vérifiez que le puzzle existe et est valide")
        if result.validation_result and result.validation_result.accuracy_percentage > 0:
            print("   • La solution partielle montre que l'approche est sur la bonne voie")
    
    print("="*70)

def _display_analyze_examples():
    """Affiche des exemples d'utilisation de la commande analyze."""
    print("🚀 EXEMPLES D'UTILISATION - Commande analyze")
    print("="*60)
    print()
    
    print("📝 Analyse basique d'un puzzle:")
    print("   python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8")
    print()
    
    print("🧠 Analyse avec apprentissage activé:")
    print("   python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8 --enable-learning")
    print()
    
    print("🔍 Analyse en mode debug avec session spécifique:")
    print("   python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8 \\")
    print("       --log-level DEBUG --chat-session blind_analysis")
    print()
    
    print("📂 Analyse avec répertoires personnalisés:")
    print("   python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8 \\")
    print("       --arc-data-dir /path/to/arc/data \\")
    print("       --results-dir ./my_results")
    print()
    
    print("🤫 Analyse silencieuse (pour scripts):")
    print("   python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8 --quiet")
    print()
    
    print("⚙️  Analyse complète avec toutes les options:")
    print("   python arc_enhanced_prompt.py analyze --puzzle-id 2204b7a8 \\")
    print("       --enable-learning --save-success \\")
    print("       --chat-session learning_extraction \\")
    print("       --log-level INFO")
    print()
    
    print("💡 CONSEILS D'UTILISATION:")
    print("   • Commencez par une analyse basique pour tester")
    print("   • Utilisez --enable-learning seulement après validation")
    print("   • Le mode --quiet est utile pour l'intégration dans des scripts")
    print("   • --log-level DEBUG aide au dépannage")
    print("   • Les résultats sont sauvegardés automatiquement en cas de succès")
    print()
    
    print("🔧 TYPES DE SESSIONS CHAT:")
    print("   • default: Session standard")
    print("   • blind_analysis: Analyse sans contexte préalable (recommandé)")
    print("   • learning_extraction: Optimisé pour l'extraction d'insights")
    print()
    
    print("📊 CODES DE SORTIE:")
    print("   • 0: Succès")
    print("   • 1: Puzzle non trouvé")
    print("   • 3: Erreur de session chat")
    print("   • 4: Erreur de parsing")
    print("   • 5: Erreur de validation")
    print("   • 6: Erreur d'apprentissage")
    print("   • 8: Erreur de configuration")
    print("="*60)

def run_test(args):
    """Exécuter les tests"""
    if args.complete:
        cmd = ['python', 'scripts/tests/test_complete_workflow.py']
    else:
        cmd = ['python', 'scripts/tests/test_enhanced_prompt.py']
    
    try:
        return subprocess.run(cmd, check=True).returncode
    except subprocess.CalledProcessError as e:
        return e.returncode

def run_cache(args):
    """Exécuter les commandes de gestion du cache"""
    if not args.cache_action:
        print("❌ Erreur: Action de cache requise")
        print("💡 Actions disponibles: stats, cleanup, preload")
        return 1
    
    if AutomatedARCAnalyzer is None:
        print("❌ Erreur: AutomatedARCAnalyzer non disponible")
        return 1
    
    try:
        from scripts.core.analysis_cache import AnalysisCache
        from pathlib import Path
        
        cache = AnalysisCache(
            cache_dir=Path(args.analysis_dir),
            max_age_days=getattr(args, 'max_age_days', 30)
        )
        
        if args.cache_action == 'stats':
            return _run_cache_stats(cache)
        elif args.cache_action == 'cleanup':
            return _run_cache_cleanup(cache, args)
        elif args.cache_action == 'preload':
            return _run_cache_preload(cache, args)
        else:
            print(f"❌ Action de cache inconnue: {args.cache_action}")
            return 1
            
    except Exception as e:
        print(f"❌ Erreur lors de la gestion du cache: {e}")
        return 1

def _run_cache_stats(cache):
    """Afficher les statistiques du cache"""
    print("📊 STATISTIQUES DU CACHE D'ANALYSES")
    print("="*50)
    
    stats = cache.get_cache_stats()
    
    print(f"Fichiers en cache: {stats.get('file_count', 0)}")
    print(f"Taille totale: {stats.get('total_size_mb', 0)} MB")
    print(f"Requêtes hits: {stats.get('hits', 0)}")
    print(f"Requêtes misses: {stats.get('misses', 0)}")
    print(f"Taux de hit: {stats.get('hit_rate_percent', 0)}%")
    print(f"Analyses obsolètes supprimées: {stats.get('obsolete_removed', 0)}")
    
    print("\n" + cache.get_cache_summary())
    return 0

def _run_cache_cleanup(cache, args):
    """Nettoyer les analyses obsolètes"""
    if args.dry_run:
        print("🔍 SIMULATION DE NETTOYAGE (dry-run)")
        print("="*40)
        print("⚠️  Aucune suppression ne sera effectuée")
        print()
    else:
        print("🧹 NETTOYAGE DU CACHE")
        print("="*30)
    
    try:
        removed_count = cache.cleanup_obsolete_analyses()
        
        if args.dry_run:
            print(f"📊 {removed_count} analyses seraient supprimées")
        else:
            print(f"✅ {removed_count} analyses obsolètes supprimées")
        
        return 0
        
    except Exception as e:
        print(f"❌ Erreur lors du nettoyage: {e}")
        return 1

def _run_cache_preload(cache, args):
    """Précharger des analyses"""
    print("⚡ PRÉCHARGEMENT D'ANALYSES")
    print("="*35)
    print(f"Puzzles à précharger: {len(args.puzzle_ids)}")
    print(f"Subset: {args.subset}")
    print()
    
    try:
        preloaded = cache.preload_analyses(args.puzzle_ids, args.subset)
        
        print(f"✅ {len(preloaded)}/{len(args.puzzle_ids)} analyses préchargées")
        
        # Afficher les détails
        for puzzle_id in args.puzzle_ids:
            if puzzle_id in preloaded:
                print(f"  ✓ {puzzle_id}: Trouvé en cache")
            else:
                print(f"  ✗ {puzzle_id}: Non trouvé en cache")
        
        return 0
        
    except Exception as e:
        print(f"❌ Erreur lors du préchargement: {e}")
        return 1

def run_env_status(args):
    """Vérifier le statut des variables d'environnement"""
    try:
        from scripts.core.env_loader import print_env_status, check_api_keys
        
        print("🔧 STATUT DE LA CONFIGURATION")
        print("=" * 50)
        
        print_env_status()
        
        # Vérifier si le fichier .env existe
        from pathlib import Path
        env_path = Path('.env')
        
        print(f"\n📁 Fichier .env: {'✅ Trouvé' if env_path.exists() else '❌ Non trouvé'}")
        
        if not env_path.exists():
            print("\n💡 Pour créer un fichier .env:")
            print("   1. Copiez le fichier .env template")
            print("   2. Remplissez vos clés API")
            print("   3. Relancez cette commande")
        
        # Compter les clés disponibles
        api_status = check_api_keys()
        available_count = sum(api_status.values())
        total_count = len(api_status)
        
        print(f"\n🎯 Résumé: {available_count}/{total_count} clés API configurées")
        
        if available_count == 0:
            print("\n⚠️  Aucune clé API configurée. Seul Ollama (local) sera disponible.")
            return 1
        elif available_count < total_count:
            print(f"\n✅ {available_count} provider(s) configuré(s). Vous pouvez commencer à utiliser le système!")
            return 0
        else:
            print("\n🎉 Toutes les clés API sont configurées!")
            return 0
            
    except ImportError:
        print("❌ Module env_loader non disponible")
        return 1
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return 1

def run_test_deepseek(args):
    """Tester DeepSeek R1 avec un prompt simple"""
    try:
        from scripts.core.openai_client import create_openai_client, OpenAIError
        
        print("🧠 Test de DeepSeek R1 (modèle de raisonnement gratuit)")
        print(f"💬 Prompt: {args.prompt}")
        print("⏳ Patience... DeepSeek R1 peut prendre 5-10 minutes pour réfléchir")
        print("-" * 60)
        
        # Créer le client OpenRouter avec DeepSeek R1
        client = create_openai_client('openrouter', model='deepseek/deepseek-r1-0528:free')
        
        # Tester la connexion d'abord
        test_result = client.test_connection()
        if not test_result['available']:
            print(f"❌ DeepSeek R1 non disponible: {test_result.get('error', 'Raison inconnue')}")
            return 1
        
        print("✅ Connexion OpenRouter OK")
        print("🧠 Envoi du prompt à DeepSeek R1...")
        
        # Envoyer le prompt
        import time
        start_time = time.time()
        
        messages = [{"role": "user", "content": args.prompt}]
        response = client.chat_completion(messages)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ Réponse reçue en {duration:.1f} secondes!")
        print("-" * 60)
        print("🤖 Réponse de DeepSeek R1:")
        print(response)
        print("-" * 60)
        print("🎉 DeepSeek R1 fonctionne correctement!")
        
        return 0
        
    except OpenAIError as e:
        print(f"❌ Erreur DeepSeek R1: {e}")
        if "credits" in str(e).lower():
            print("💡 Vérifiez vos crédits OpenRouter: https://openrouter.ai/settings/credits")
        elif "not found" in str(e).lower():
            print("💡 Le modèle DeepSeek R1 gratuit n'est peut-être plus disponible")
        return 1
    except ImportError:
        print("❌ Module OpenRouter non disponible")
        return 1
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())