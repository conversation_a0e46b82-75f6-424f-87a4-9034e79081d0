"""
Client Hugging Face pour l'intégration avec le système d'analyse ARC AGI.

Ce module fournit une interface pour communiquer avec l'API Hugging Face Inference,
permettant d'utiliser des modèles hébergés sur Hugging Face Hub.
"""

import os
import json
import time
from typing import Dict, Any, Optional, List
from .constants import DefaultConfigurations
from .env_loader import get_api_key, get_default_model


class HuggingFaceError(Exception):
    """Exception levée lors d'erreurs avec Hugging Face."""
    pass


class HuggingFaceClient:
    """Client pour communiquer avec l'API Hugging Face."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialise le client Hugging Face.
        
        Args:
            config: Configuration Hugging Face (optionnel)
        """
        self.config = config or DefaultConfigurations.HUGGINGFACE_CONFIG.copy()
        default_model = get_default_model('huggingface') or 'microsoft/DialoGPT-large'
        self.model = self.config.get('model', default_model)
        self.temperature = self.config.get('temperature', 0.1)
        self.max_tokens = self.config.get('max_tokens', 4000)
        
        # Timeout personnalisé depuis .env
        import os
        default_timeout = int(os.getenv('HUGGINGFACE_TIMEOUT', '120'))
        self.timeout = self.config.get('timeout', default_timeout)
        
        # Configuration API
        self.api_key = get_api_key('huggingface')
        self.base_url = "https://api-inference.huggingface.co/models"
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        if not self.api_key:
            raise HuggingFaceError(
                "Clé API Hugging Face manquante. "
                "Définissez HUGGINGFACE_API_KEY dans vos variables d'environnement."
            )
    
    def is_available(self) -> bool:
        """
        Vérifie si l'API Hugging Face est disponible.
        
        Returns:
            bool: True si l'API répond, False sinon
        """
        try:
            import requests
            # Test avec un modèle simple
            response = requests.post(
                f"{self.base_url}/gpt2",
                headers=self.headers,
                json={"inputs": "test"},
                timeout=5
            )
            return response.status_code in [200, 503]  # 503 = modèle en cours de chargement
        except Exception:
            return False
    
    def is_model_available(self, model_name: str) -> bool:
        """
        Vérifie si un modèle spécifique est disponible.
        
        Args:
            model_name: Nom du modèle à vérifier
            
        Returns:
            bool: True si le modèle est disponible
        """
        try:
            import requests
            response = requests.post(
                f"{self.base_url}/{model_name}",
                headers=self.headers,
                json={"inputs": "test"},
                timeout=10
            )
            return response.status_code in [200, 503]
        except Exception:
            return False
    
    def generate_response(self, prompt: str, model: Optional[str] = None) -> str:
        """
        Génère une réponse avec Hugging Face.
        
        Args:
            prompt: Le prompt à envoyer
            model: Modèle à utiliser (optionnel, utilise celui de la config)
            
        Returns:
            str: Réponse générée par le modèle
            
        Raises:
            HuggingFaceError: Si erreur lors de la génération
        """
        model_to_use = model or self.model
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "temperature": self.temperature,
                "max_new_tokens": self.max_tokens,
                "return_full_text": False
            }
        }
        
        return self._execute_with_retry(
            lambda: self._generate_response_internal(payload, model_to_use),
            max_retries=3,
            operation_name="generate_response"
        )

    def _execute_with_retry(self, operation, max_retries: int = 3, operation_name: str = "operation"):
        """Exécute une opération avec retry logic robuste."""
        import time
        import random
        import requests

        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # Délai exponentiel avec jitter
                    delay = min(2 ** attempt + random.uniform(0, 1), 30)
                    print(f"⏳ Tentative {attempt + 1}/{max_retries + 1} dans {delay:.1f}s...")
                    time.sleep(delay)

                return operation()

            except requests.exceptions.Timeout as e:
                last_exception = e
                print(f"⚠️ Timeout lors de {operation_name} (tentative {attempt + 1})")
                if attempt == max_retries:
                    raise HuggingFaceError(f"Timeout définitif après {max_retries + 1} tentatives")
                continue

            except requests.exceptions.ConnectionError as e:
                last_exception = e
                print(f"⚠️ Erreur de connexion lors de {operation_name} (tentative {attempt + 1})")
                if attempt == max_retries:
                    raise HuggingFaceError(f"Erreur de connexion définitive: {e}")
                continue

            except requests.exceptions.HTTPError as e:
                last_exception = e
                if hasattr(e, 'response') and e.response is not None:
                    status_code = e.response.status_code
                    # Erreurs 5xx et 503: retry, erreurs 4xx: pas de retry
                    if status_code >= 500 or status_code == 503:
                        print(f"⚠️ Erreur HTTP {status_code} lors de {operation_name} (tentative {attempt + 1})")
                        if attempt == max_retries:
                            raise HuggingFaceError(f"Erreur HTTP {status_code} définitive")
                        continue
                    else:
                        # Erreur 4xx non récupérable
                        try:
                            error_data = e.response.json()
                            error_msg = error_data.get('error', str(e))
                            raise HuggingFaceError(f"Erreur API: {error_msg}")
                        except:
                            raise HuggingFaceError(f"Erreur HTTP {status_code}: {e.response.text}")
                else:
                    raise HuggingFaceError(f"Erreur HTTP: {e}")

            except HuggingFaceError:
                # Les erreurs HuggingFace sont déjà formatées, les relancer directement
                raise

            except Exception as e:
                last_exception = e
                print(f"⚠️ Erreur inattendue lors de {operation_name}: {e}")
                if attempt == max_retries:
                    raise HuggingFaceError(f"Erreur inattendue définitive: {e}")
                continue

        # Ne devrait jamais arriver
        raise HuggingFaceError(f"Échec définitif de {operation_name}: {last_exception}")

    def _generate_response_internal(self, payload, model_to_use):
        """Implémentation interne de la génération de réponse."""
        import requests
        import json

        response = requests.post(
            f"{self.base_url}/{model_to_use}",
            headers=self.headers,
            json=payload,
            timeout=self.timeout
        )

        if response.status_code == 503:
            # Modèle en cours de chargement
            try:
                error_data = response.json()
                if "loading" in error_data.get("error", "").lower():
                    estimated_time = error_data.get("estimated_time", 20)
                    raise HuggingFaceError(f"Modèle en cours de chargement, temps estimé: {estimated_time}s")
            except json.JSONDecodeError:
                pass

        response.raise_for_status()
        data = response.json()

        if isinstance(data, list) and len(data) > 0:
            if isinstance(data[0], dict) and 'generated_text' in data[0]:
                return data[0]['generated_text'].strip()
            elif isinstance(data[0], str):
                return data[0].strip()

        raise HuggingFaceError(f"Format de réponse inattendu: {data}")
    
    def chat_completion(self, messages: List[Dict[str, str]], model: Optional[str] = None) -> str:
        """
        Génère une réponse en mode chat.
        
        Args:
            messages: Liste des messages (format OpenAI-like)
            model: Modèle à utiliser (optionnel)
            
        Returns:
            str: Réponse du modèle
            
        Raises:
            HuggingFaceError: Si erreur lors de la génération
        """
        model_to_use = model or self.model
        
        # Convertir les messages en prompt simple pour Hugging Face
        prompt_parts = []
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"Human: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        prompt = "\n\n".join(prompt_parts) + "\n\nAssistant:"
        
        return self.generate_response(prompt, model_to_use)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Teste la connexion à Hugging Face et retourne des informations.
        
        Returns:
            dict: Informations sur la connexion et les modèles
        """
        result = {
            'available': False,
            'models': [self.model],  # HF ne permet pas de lister facilement
            'current_model': self.model,
            'provider': 'huggingface',
            'error': None
        }
        
        try:
            if not self.api_key:
                result['error'] = "Clé API Hugging Face manquante"
                return result
            
            if not self.is_available():
                result['error'] = "API Hugging Face non accessible"
                return result
            
            result['available'] = True
            
            # Tester le modèle actuel
            if not self.is_model_available(self.model):
                result['error'] = f"Modèle '{self.model}' non disponible"
            
        except Exception as e:
            result['error'] = str(e)
        
        return result


def create_huggingface_client(model: str = None, api_key: str = None) -> HuggingFaceClient:
    """
    Crée un client Hugging Face avec une configuration personnalisée.
    
    Args:
        model: Nom du modèle à utiliser
        api_key: Clé API (optionnel, utilise les variables d'environnement par défaut)
        
    Returns:
        HuggingFaceClient: Instance configurée du client
    """
    config = DefaultConfigurations.HUGGINGFACE_CONFIG.copy()
    
    if model:
        config['model'] = model
    
    # Définir temporairement la clé API si fournie
    if api_key:
        os.environ['HUGGINGFACE_API_KEY'] = api_key
    
    return HuggingFaceClient(config)