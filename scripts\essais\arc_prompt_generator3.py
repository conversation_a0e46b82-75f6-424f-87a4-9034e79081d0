import json
import os
import sys

def visualize_grid(grid, background=0):
    """Convertit une grille en représentation visuelle intuitive avec emojis"""
    color_map = { 
        0: "▪️", # Fond ⬛
        1: "🟦", # Bleu
        2: "🟥", # Rouge
        3: "🟩", # Vert
        4: "🟨", # <PERSON><PERSON><PERSON>
        5: "🔳", # Gris/Noir
        6: "🟪", # Magenta/Violet
        7: "🟧", # Orange
        8: "⬜", # <PERSON><PERSON>/Blanc
        9: "🟫", # <PERSON>ron
    }

    visual = []
    for row in grid:
        visual_row = []
        for cell in row:
            if cell == background:
                visual_row.append("  ")
            else:
                visual_row.append(color_map.get(cell, "  "))
        visual.append(" ".join(visual_row))
    
    return "\n".join(visual)

def extract_key_info(analysis_data):
    """Extrait les informations critiques de l'analyse"""
    # Extraire les informations de base
    puzzle_id = analysis_data["puzzle_id"]
    background = analysis_data["raw_analysis"]["patterns"]["motif"]["background_colors"][0]
    
    # Extraire les couleurs du motif
    motif_colors = analysis_data["raw_analysis"]["patterns"]["motif"]["motif_colors"]
    motif_type = analysis_data["raw_analysis"]["patterns"]["motif"]["type"]
    
    # Déterminer le type de transformation
    transformation_type = "movement"
    direction = [0, 0]  # [dx, dy]
    mechanism = "unknown"
    edge_behavior = "unknown"
    
    # Déterminer la direction du mouvement
    if "common_color_changes" in analysis_data["raw_analysis"]["diff_analysis"]:
        changes = analysis_data["raw_analysis"]["diff_analysis"]["common_color_changes"]
        
        # Analyser les changements pour déterminer la direction
        for change, count in changes.items():
            if "→" in change:
                from_color, to_color = change.split("→")
                if from_color == "0" and int(to_color) in motif_colors:
                    # Déplacement vers le bas ou à droite
                    direction = [1, 0] if count > 5 else [0, 1]
                elif to_color == "0" and int(from_color) in motif_colors:
                    # Déplacement vers le haut ou à gauche
                    direction = [-1, 0] if count > 5 else [0, -1]
    
    # Déterminer le mécanisme
    if "gravity" in analysis_data.get("tags", []):
        mechanism = "gravity"
    elif "sliding" in analysis_data.get("tags", []):
        mechanism = "sliding"
    elif "in_place_transformation" in analysis_data["raw_analysis"]["transformations"]["structural"]:
        mechanism = "in_place"
    
    # Déterminer le comportement aux bords
    if "stops_at_obstacle" in analysis_data.get("tags", []):
        edge_behavior = "stops_at_obstacle"
    else:
        edge_behavior = "wraps_around"
    
    # Déterminer les propriétés
    color_conservation = True
    structure_conservation = True
    
    # Extraire les tags pertinents
    tags = []
    if mechanism != "unknown":
        tags.append(mechanism)
    if direction[0] != 0:
        tags.append("déplacement_horizontal")
    if direction[1] != 0:
        tags.append("déplacement_vertical")
    
    return {
        "puzzle_id": puzzle_id,
        "background": background,
        "motif": {
            "colors": motif_colors,
            "type": motif_type
        },
        "transformation": {
            "type": transformation_type,
            "direction": direction,
            "mechanism": mechanism,
            "edge_behavior": edge_behavior
        },
        "properties": {
            "color_conservation": color_conservation,
            "structure_conservation": structure_conservation
        },
        "tags": tags
    }

def generate_arc_prompt(puzzle_data, analysis_data):
    """Génère un prompt optimisé pour la résolution ARC AGI"""
    # Extraire les informations critiques
    key_info = extract_key_info(analysis_data)
    
    # Construire le prompt
    prompt = f"PUZZLE ID: {key_info['puzzle_id']}\n"
    prompt += f"BACKGROUND: {key_info['background']} = [ ]\n\n"
    
    # Ajouter les exemples d'entraînement
    for i, example in enumerate(puzzle_data["train"]):
        prompt += f"INPUT EXAMPLE {i+1}:\n"
        prompt += visualize_grid(example["input"], key_info["background"])
        prompt += "\n\n"
        
        prompt += f"OUTPUT EXAMPLE {i+1}:\n"
        prompt += visualize_grid(example["output"], key_info["background"])
        prompt += "\n\n"
    
    # Ajouter les observations clés
    prompt += "KEY OBSERVATIONS:\n"
    
    # Observation 1: Type de motif
    color_names = [f"{c} ({['🔴','🟠','🟡','🟢','🔵','🟣','🟤','⚫','⚪'][c-1]})" for c in key_info["motif"]["colors"]]
    prompt += f"- Motif de type '{key_info['motif']['type']}' avec les couleurs: {', '.join(color_names)}\n"
    
    # Observation 2: Transformation
    dx, dy = key_info["transformation"]["direction"]
    direction_desc = []
    if dx > 0:
        direction_desc.append(f"{dx} position(s) vers la droite")
    elif dx < 0:
        direction_desc.append(f"{abs(dx)} position(s) vers la gauche")
    if dy > 0:
        direction_desc.append(f"{dy} position(s) vers le bas")
    elif dy < 0:
        direction_desc.append(f"{abs(dy)} position(s) vers le haut")
    
    direction_str = " et ".join(direction_desc) if direction_desc else "aucun déplacement"
    prompt += f"- Transformation: déplacement {direction_str} sous l'effet de {key_info['transformation']['mechanism']}\n"
    
    # Observation 3: Propriétés
    prompt += "- Propriétés conservées: "
    properties = []
    if key_info["properties"]["color_conservation"]:
        properties.append("couleurs")
    if key_info["properties"]["structure_conservation"]:
        properties.append("structure")
    prompt += ", ".join(properties) if properties else "aucune"
    prompt += "\n"
    
    # Observation 4: Comportement aux bords
    edge_behavior = {
        "stops_at_obstacle": "s'arrête à l'obstacle",
        "wraps_around": "revient de l'autre côté"
    }.get(key_info["transformation"]["edge_behavior"], "inconnu")
    prompt += f"- Comportement aux bords: {edge_behavior}\n\n"
    
    # Ajouter la règle de transformation
    prompt += "RULE: "
    
    # Générer une description de règle naturelle
    if key_info["transformation"]["mechanism"] == "gravity":
        prompt += f"Les motifs se déplacent vers le bas sous l'effet de la gravité jusqu'à rencontrer un obstacle ou le bord inférieur de la grille."
    elif key_info["transformation"]["mechanism"] == "sliding":
        prompt += f"Les motifs glissent horizontalement jusqu'à atteindre un obstacle ou le bord de la grille."
    else:
        prompt += f"Les motifs se déplacent de {dx}, {dy} positions selon la règle de transformation identifiée."
    
    prompt += "\n\n"
    
    # Ajouter le test input
    prompt += "INPUT TEST:\n"
    prompt += visualize_grid(puzzle_data["test"][0]["input"], key_info["background"])
    prompt += "\n\n"
    
    # Ajouter l'instruction finale
    prompt += "APPLY THE RULE TO THE TEST INPUT:\n"
    
    return prompt, key_info

def save_analysis_report(analysis_data, output_file):
    """Sauvegarde un rapport d'analyse structuré"""
    report = {
        "puzzle_id": analysis_data["puzzle_id"],
        "background": analysis_data["raw_analysis"]["patterns"]["motif"]["background_colors"][0],
        "motif": {
            "colors": analysis_data["raw_analysis"]["patterns"]["motif"]["motif_colors"],
            "type": analysis_data["raw_analysis"]["patterns"]["motif"]["type"]
        },
        "transformation": {
            "type": "movement",
            "direction": [0, 0],  # À compléter avec une analyse plus détaillée
            "mechanism": "gravity" if "gravity" in analysis_data.get("tags", []) else "in_place",
            "edge_behavior": "stops_at_obstacle"
        },
        "properties": {
            "color_conservation": True,
            "structure_conservation": True
        },
        "tags": analysis_data.get("tags", [])
    }
    
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    return report

def main():
    if len(sys.argv) < 3:
        print("Usage: python arc_prompt_generator.py <puzzle_json> <analysis_json> [output_file]")
        print("Example: python arc_prompt_generator.py 025d127b.json 025d127b_analysis.json prompt_025d127b.txt")
        return
    
    puzzle_file = sys.argv[1]
    analysis_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    # Charger les données
    with open(puzzle_file, 'r') as f:
        puzzle_data = json.load(f)
    
    with open(analysis_file, 'r') as f:
        analysis_data = json.load(f)
    
    # Générer le prompt
    prompt, key_info = generate_arc_prompt(puzzle_data, analysis_data)
    
    # Afficher ou sauvegarder le prompt
    if output_file:
        with open(output_file, 'w') as f:
            f.write(prompt)
        print(f"Prompt généré et sauvegardé dans {output_file}")
        
        # Sauvegarder aussi l'analyse structurée
        report_file = os.path.splitext(output_file)[0] + "_analysis.json"
        with open(report_file, 'w') as f:
            json.dump(key_info, f, indent=2)
        print(f"Analyse structurée sauvegardée dans {report_file}")
    else:
        print(prompt)
    
    # Afficher un résumé des informations clés
    print("\n" + "="*50)
    print("RÉSUMÉ DES INFORMATIONS CLÉS:")
    print(f"- Puzzle ID: {key_info['puzzle_id']}")
    print(f"- Fond: {key_info['background']}")
    print(f"- Couleurs du motif: {key_info['motif']['colors']}")
    print(f"- Type de motif: {key_info['motif']['type']}")
    print(f"- Mécanisme: {key_info['transformation']['mechanism']}")
    print(f"- Direction: {key_info['transformation']['direction']}")
    print(f"- Tags: {', '.join(key_info['tags'])}")
    print("="*50)

if __name__ == "__main__":
    main()