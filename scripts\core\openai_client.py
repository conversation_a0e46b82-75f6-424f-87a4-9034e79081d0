"""
Client OpenAI et compatibles (OpenRouter, etc.) pour l'intégration avec le système d'analyse ARC AGI.

Ce module fournit une interface pour communiquer avec les APIs compatibles OpenAI,
incluant OpenAI, OpenRouter, et autres services similaires.
"""

import os
import json
import requests
import time
from typing import Dict, Any, Optional, List
from .constants import DefaultConfigurations
from .env_loader import get_api_key, get_default_model


class OpenAIError(Exception):
    """Exception levée lors d'erreurs avec les APIs OpenAI."""
    pass


class OpenAIClient:
    """Client pour communiquer avec les APIs compatibles OpenAI."""
    
    def __init__(self, provider: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialise le client OpenAI.
        
        Args:
            provider: Type de provider ('openai', 'openrouter')
            config: Configuration du client (optionnel)
        """
        self.provider = provider
        self.config = config or self._get_default_config(provider)
        
        # Configuration commune avec modèles par défaut depuis .env
        default_model = get_default_model(provider) or ('gpt-4o-mini' if provider == 'openai' else 'qwen/qwen3-30b-a3b:free')
        self.model = self.config.get('model', default_model)
        self.temperature = self.config.get('temperature', 0.1)
        
        # Charger le .env AVANT de lire les variables
        from .env_loader import load_env_file
        load_env_file()  # S'assurer que le .env est chargé
        
        # Max tokens personnalisé depuis .env (os déjà importé plus haut)
        if provider == 'openrouter':
            max_tokens_str = os.getenv('OPENROUTER_MAX_TOKENS', '16000').split('#')[0].strip()
            default_max_tokens = int(max_tokens_str)
        else:
            max_tokens_str = os.getenv('OPENAI_MAX_TOKENS', '8000').split('#')[0].strip()
            default_max_tokens = int(max_tokens_str)
        self.max_tokens = self.config.get('max_tokens', default_max_tokens)
        
        if provider == 'openrouter':
            timeout_str = os.getenv('OPENROUTER_TIMEOUT', '300').split('#')[0].strip()
            default_timeout = int(timeout_str)
        else:
            timeout_str = os.getenv('OPENAI_TIMEOUT', '60').split('#')[0].strip()
            default_timeout = int(timeout_str)
        self.timeout = self.config.get('timeout', default_timeout)
        
        print(f"🕐 Timeout configuré pour {provider}: {self.timeout}s")
        print(f"📏 Max tokens configuré: {self.max_tokens}")
        
        # Configuration spécifique au provider
        if provider == 'openrouter':
            self.base_url = self.config.get('base_url', 'https://openrouter.ai/api/v1')
            self.api_key = get_api_key('openrouter')
            self.headers = {
                'Authorization': f'Bearer {self.api_key}',
                'HTTP-Referer': 'https://github.com/your-repo',  # Requis par OpenRouter
                'X-Title': 'ARC AGI Solver'
            }
        elif provider == 'openai':
            self.base_url = self.config.get('base_url', 'https://api.openai.com/v1')
            self.api_key = get_api_key('openai')
            self.headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
        elif provider == 'groq':  # groq
            self.base_url = self.config.get('base_url', 'https://api.groq.com/openai/v1')
            self.api_key = get_api_key('groq')
            self.headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
        else:
            raise ValueError(f"Provider non supporté: {provider}")
        
        # Vérifier que la clé API est disponible
        if not self.api_key:
            raise OpenAIError(f"Clé API manquante pour {provider}. "
                            f"Définissez {provider.upper()}_API_KEY dans vos variables d'environnement.")
    
    def _get_default_config(self, provider: str) -> Dict[str, Any]:
        """Retourne la configuration par défaut selon le provider."""
        if provider == 'openrouter':
            return DefaultConfigurations.OPENROUTER_CONFIG.copy()
        else:
            return DefaultConfigurations.OPENAI_CONFIG.copy()
    
    def is_available(self) -> bool:
        """
        Vérifie si l'API est disponible.
        
        Returns:
            bool: True si l'API répond, False sinon
        """
        try:
            import requests
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=5
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def list_models(self) -> List[str]:
        """
        Liste les modèles disponibles.
        
        Returns:
            list: Liste des modèles disponibles
            
        Raises:
            OpenAIError: Si impossible de récupérer la liste
        """
        try:
            import requests
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            return [model['id'] for model in data.get('data', [])]
        except Exception as e:
            raise OpenAIError(f"Impossible de lister les modèles: {e}")
    
    def chat_completion(self, messages: List[Dict[str, str]], model: Optional[str] = 
                        None, retry_on_truncation: bool = True, max_continuations: int = 3) -> str:
        """
        Génère une réponse en mode chat.
        
        Args:
            messages: Liste des messages au format OpenAI
            model: Modèle à utiliser (optionnel)
            retry_on_truncation: Retry si la réponse semble tronquée
            
        Returns:
            str: Réponse du modèle
            
        Raises:
            OpenAIError: Si erreur lors de la génération
        """
        try:
            import requests
            
            model_to_use = model or self.model
            
            payload = {
                "model": model_to_use,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": True  # Activer le streaming
            }
            
            print(f"🌐 Envoi requête à {self.base_url} avec timeout {self.timeout}s...")
            print(f"📝 Modèle: {model_to_use}")
            print(f"🔄 Streaming activé - affichage en temps réel...")
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout,
                stream=True  # Streaming côté requests
            )
            
            print(f"📡 Connexion établie - Status: {response.status_code}")
            
            # Traiter le streaming
            if response.status_code == 200:
                result = self._handle_streaming_response(response, model_to_use)
                
                # Vérifier si la réponse est tronquée et essayer de continuer
                if retry_on_truncation and self._is_response_truncated_by_length(result):
                    print("⚠️ Réponse tronquée par limite de tokens, tentative de continuation...")
                    
                    # Essayer de continuer la réponse
                    continuation_count = 0
                    full_response = result
                    
                    while continuation_count < max_continuations and self._is_response_truncated_by_length(full_response):
                        continuation_count += 1
                        print(f"🔄 Continuation {continuation_count}/{max_continuations}...")
                        
                        # Créer un nouveau message pour continuer
                        continuation_messages = messages.copy()
                        continuation_messages.append({
                            "role": "assistant", 
                            "content": full_response
                        })
                        continuation_messages.append({
                            "role": "user", 
                            "content": "Continue ton analyse là où tu t'es arrêté. Termine par la solution finale du puzzle."
                        })
                        
                        # Demander la continuation
                        continuation = self.chat_completion(
                            continuation_messages, 
                            model_to_use, 
                            retry_on_truncation=False,  # Éviter la récursion infinie
                            max_continuations=0
                        )
                        
                        # Combiner les réponses
                        full_response = full_response + "\n\n" + continuation
                        print(f"📝 Réponse étendue: {len(full_response)} caractères")
                    
                    return full_response
                
                return result
            else:
                response.raise_for_status()
            
        except requests.Timeout:
            raise OpenAIError(f"Timeout après {self.timeout}s")
        except requests.RequestException as e:
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_msg = error_data.get('error', {}).get('message', str(e))
                    raise OpenAIError(f"Erreur API: {error_msg}")
                except json.JSONDecodeError:
                    raise OpenAIError(f"Erreur HTTP {e.response.status_code}: {e.response.text}")
            else:
                raise OpenAIError(f"Erreur de requête: {e}")
        except json.JSONDecodeError as e:
            raise OpenAIError(f"Réponse JSON invalide: {e}")
        except Exception as e:
            raise OpenAIError(f"Erreur inattendue: {e}")

    def _execute_with_retry(self, operation, max_retries: int = 3, operation_name: str = "operation"):
        """Exécute une opération avec retry logic robuste."""
        import time
        import random
        import requests

        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # Délai exponentiel avec jitter
                    delay = min(2 ** attempt + random.uniform(0, 1), 30)
                    print(f"⏳ Tentative {attempt + 1}/{max_retries + 1} dans {delay:.1f}s...")
                    time.sleep(delay)

                return operation()

            except requests.exceptions.Timeout as e:
                last_exception = e
                print(f"⚠️ Timeout lors de {operation_name} (tentative {attempt + 1})")
                if attempt == max_retries:
                    raise OpenAIError(f"Timeout définitif après {max_retries + 1} tentatives")
                continue

            except requests.exceptions.ConnectionError as e:
                last_exception = e
                print(f"⚠️ Erreur de connexion lors de {operation_name} (tentative {attempt + 1})")
                if attempt == max_retries:
                    raise OpenAIError(f"Erreur de connexion définitive: {e}")
                continue

            except requests.exceptions.HTTPError as e:
                last_exception = e
                if hasattr(e, 'response') and e.response is not None:
                    status_code = e.response.status_code
                    # Erreurs 5xx: retry, erreurs 4xx: pas de retry (sauf 429)
                    if status_code >= 500 or status_code == 429:
                        print(f"⚠️ Erreur HTTP {status_code} lors de {operation_name} (tentative {attempt + 1})")
                        if attempt == max_retries:
                            raise OpenAIError(f"Erreur HTTP {status_code} définitive")
                        continue
                    else:
                        # Erreur 4xx non récupérable
                        try:
                            error_data = e.response.json()
                            error_msg = error_data.get('error', {}).get('message', str(e))
                            raise OpenAIError(f"Erreur API: {error_msg}")
                        except:
                            raise OpenAIError(f"Erreur HTTP {status_code}: {e.response.text}")
                else:
                    raise OpenAIError(f"Erreur HTTP: {e}")

            except OpenAIError:
                # Les erreurs OpenAI sont déjà formatées, les relancer directement
                raise

            except Exception as e:
                last_exception = e
                print(f"⚠️ Erreur inattendue lors de {operation_name}: {e}")
                if attempt == max_retries:
                    raise OpenAIError(f"Erreur inattendue définitive: {e}")
                continue

        # Ne devrait jamais arriver
        raise OpenAIError(f"Échec définitif de {operation_name}: {last_exception}")

    def _save_raw_response(self, response_text: str, model_name: str) -> None:
        """Sauvegarde la réponse brute pour debug/étude."""
        try:
            from pathlib import Path
            from datetime import datetime
            
            # Créer le dossier de sauvegarde
            save_dir = Path("arc_results/raw_responses")
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # Nom de fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_clean = model_name.replace("/", "_").replace(":", "_")
            filename = f"{timestamp}_{model_clean}_response.txt"
            
            # Sauvegarder
            with open(save_dir / filename, 'w', encoding='utf-8') as f:
                f.write(f"# Réponse IA brute\n")
                f.write(f"# Modèle: {model_name}\n")
                f.write(f"# Timestamp: {datetime.now().isoformat()}\n")
                f.write(f"# Taille: {len(response_text)} caractères\n")
                f.write(f"# Provider: {self.provider}\n")
                f.write(f"\n{'='*80}\n\n")
                f.write(response_text)
            
            print(f"💾 Réponse sauvegardée: {save_dir / filename}")
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde réponse: {e}")
    
    def _handle_streaming_response(self, response, model_name: str) -> str:
        """Gère la réponse en streaming avec gestion robuste des chunks."""
        import json
        import time

        full_content = ""
        full_reasoning = ""
        chunk_count = 0
        last_chunk_time = time.time()

        print("🔄 Réception en cours", end="", flush=True)

        try:
            done_received = False
            finish_reason_received = None
            stream_ended = False

            # Buffer pour gérer les chunks partiels
            buffer = ""

            # Lire le stream avec timeout adaptatif
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    buffer += chunk
                    last_chunk_time = time.time()

                    # Traiter les lignes complètes dans le buffer
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()

                        if line and line.startswith('data: '):
                            data_str = line[6:]  # Enlever 'data: '

                            if data_str.strip() == '[DONE]':
                                done_received = True
                                print(" [DONE]", end="", flush=True)
                                # Continuer à lire pour s'assurer qu'il n'y a plus de données
                                continue

                            try:
                                chunk_data = json.loads(data_str)

                                if 'choices' in chunk_data and chunk_data['choices']:
                                    choice = chunk_data['choices'][0]

                                    # Gérer finish_reason
                                    if 'finish_reason' in choice and choice['finish_reason']:
                                        if not finish_reason_received:
                                            finish_reason_received = choice['finish_reason']
                                            print(f" [Fin: {finish_reason_received}]", end="", flush=True)

                                        # Si finish_reason est 'stop', on peut arrêter de lire
                                        if choice['finish_reason'] == 'stop':
                                            stream_ended = True

                                    if 'delta' in choice:
                                        delta = choice['delta']

                                        # Contenu normal
                                        if 'content' in delta and delta['content']:
                                            full_content += delta['content']

                                        # Raisonnement (pour les modèles comme Qwen)
                                        if 'reasoning' in delta and delta['reasoning']:
                                            full_reasoning += delta['reasoning']

                                    # Afficher le progrès
                                    chunk_count += 1
                                    if chunk_count % 10 == 0:
                                        print(".", end="", flush=True)

                            except json.JSONDecodeError:
                                # Ignorer les chunks JSON invalides mais continuer
                                continue
                            except Exception:
                                # Ignorer les erreurs de chunk mais continuer
                                continue

                # Vérifier timeout adaptatif
                current_time = time.time()
                if current_time - last_chunk_time > 30:  # 30s sans nouveau chunk
                    print(" [Timeout stream]", end="", flush=True)
                    break

                # Si stream_ended et done_received, on peut arrêter
                if stream_ended and done_received:
                    break

            # Traiter le buffer restant
            if buffer.strip():
                for line in buffer.split('\n'):
                    line = line.strip()
                    if line and line.startswith('data: '):
                        data_str = line[6:]
                        if data_str.strip() != '[DONE]':
                            try:
                                chunk_data = json.loads(data_str)
                                if 'choices' in chunk_data and chunk_data['choices']:
                                    choice = chunk_data['choices'][0]
                                    if 'delta' in choice:
                                        delta = choice['delta']
                                        if 'content' in delta and delta['content']:
                                            full_content += delta['content']
                                        if 'reasoning' in delta and delta['reasoning']:
                                            full_reasoning += delta['reasoning']
                                        chunk_count += 1
                            except:
                                continue

            print(f"\n✅ Streaming terminé - {chunk_count} chunks reçus")

            # Analyser la qualité de la réponse
            total_chars = len(full_content) + len(full_reasoning)
            if finish_reason_received == 'length':
                print(f"⚠️ Réponse tronquée par limite de tokens (finish_reason: length)")
            elif finish_reason_received == 'stop':
                print(f"✅ Réponse complète (finish_reason: stop)")
            elif not done_received and total_chars > 0:
                print(f"⚠️ [DONE] non reçu - stream possiblement incomplet")

            # Choisir la meilleure réponse disponible
            if full_reasoning and len(full_reasoning) > len(full_content):
                final_response = full_reasoning
                print("🧠 Utilisation du champ 'reasoning' (plus complet)")
            elif full_content:
                final_response = full_content
                print("📝 Utilisation du champ 'content'")
            else:
                final_response = full_reasoning or full_content or ""
                print("⚠️ Réponse vide ou incomplète")

            # Sauvegarder la réponse
            self._save_raw_response(final_response, model_name)

            return final_response

        except Exception as e:
            print(f"\n❌ Erreur streaming: {e}")
            # Fallback robuste: essayer de lire la réponse complète
            try:
                # Réinitialiser la position du stream si possible
                if hasattr(response, 'raw'):
                    response.raw.seek(0)

                response_text = response.text
                if response_text:
                    data = json.loads(response_text)
                    if 'choices' in data and data['choices']:
                        choice = data['choices'][0]
                        if 'message' in choice:
                            message = choice['message']
                            content = message.get('content', '').strip()
                            reasoning = message.get('reasoning', '').strip()
                            final_response = reasoning if reasoning and len(reasoning) > len(content) else content
                            if final_response:
                                self._save_raw_response(final_response, model_name)
                                print("🔄 Fallback: réponse récupérée en mode non-streaming")
                                return final_response
            except:
                pass

            raise OpenAIError(f"Erreur lors du streaming: {e}")
    
    def _is_response_truncated_by_length(self, response: str) -> bool:
        """Détecte si une réponse a été tronquée par la limite de tokens."""
        if not response or len(response) < 100:
            return True
        
        # Indicateurs spécifiques de troncature par limite de tokens
        length_truncation_indicators = [
            # Phrases qui se terminent abruptement
            response.endswith("So"),
            response.endswith("But"),
            response.endswith("And"),
            response.endswith("The"),
            response.endswith("For"),
            response.endswith("If"),
            response.endswith("row 1 is [1,"),
            response.endswith("For example,"),
            response.endswith("Maybe I"),
            response.endswith("This is not"),
            response.endswith("But I"),
            response.endswith("and "),
            response.endswith("the "),
            response.endswith("are "),
            response.endswith("by "),
            # Réponse longue sans conclusion claire
            len(response) > 8000 and not any(end in response.lower()[-1000:] for end in [
                "conclusion", "final", "result", "output", "solution", 
                "réponse finale", "test output", "✅", "answer", "grid"
            ])
        ]
        
        return any(length_truncation_indicators)
    
    def _is_response_truncated(self, response: str) -> bool:
        """Détecte si une réponse semble tronquée (méthode générale)."""
        return self._is_response_truncated_by_length(response)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Teste la connexion avec gestion d'erreurs améliorée.
        
        Returns:
            dict: Résultat du test avec détails
        """
        try:
            # Test simple avec un prompt minimal
            test_messages = [{"role": "user", "content": "Dis juste 'OK'"}]
            
            # Configuration de test avec tokens limités
            test_payload = {
                "model": self.model,
                "messages": test_messages,
                "temperature": 0,
                "max_tokens": 10,
                "stream": False  # Pas de streaming pour le test
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data['choices'][0]['message']['content']
                
                return {
                    'available': True,
                    'model': self.model,
                    'provider': self.provider,
                    'response': content.strip(),
                    'status': 'OK'
                }
            else:
                return {
                    'available': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'provider': self.provider
                }
                
        except requests.exceptions.Timeout:
            return {
                'available': False,
                'error': f"Timeout après 30s",
                'provider': self.provider
            }
        except Exception as e:
            return {
                'available': False,
                'error': str(e),
                'provider': self.provider
            }


def create_openai_client(provider: str, model: Optional[str] = None, api_key: Optional[str] = None) -> OpenAIClient:
    """
    Crée un client OpenAI avec une configuration personnalisée.
    
    Args:
        provider: Type de provider ('openai', 'openrouter')
        model: Nom du modèle à utiliser
        api_key: Clé API (optionnel, utilise les variables d'environnement par défaut)
        
    Returns:
        OpenAIClient: Instance configurée du client
    """
    config = {}
    
    if provider == 'openrouter':
        config = DefaultConfigurations.OPENROUTER_CONFIG.copy()
    else:
        config = DefaultConfigurations.OPENAI_CONFIG.copy()
    
    if model:
        config['model'] = model
    
    # Définir temporairement la clé API si fournie
    if api_key:
        env_var = f"{provider.upper()}_API_KEY"
        os.environ[env_var] = api_key
    
    return OpenAIClient(provider, config)
