"""
Client Ollama pour l'intégration avec le système d'analyse ARC AGI.

Ce module fournit une interface pour communiquer avec <PERSON>,
permettant d'utiliser des modèles locaux pour l'analyse des puzzles.
"""

import json
import requests
import time
from typing import Dict, Any, Optional
from .constants import DefaultConfigurations


class OllamaError(Exception):
    """Exception levée lors d'erreurs avec Ollama."""
    pass


class OllamaClient:
    """Client pour communiquer avec Ollama."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialise le client Ollama.
        
        Args:
            config: Configuration Ollama (optionnel)
        """
        self.config = config or DefaultConfigurations.OLLAMA_CONFIG.copy()
        self.base_url = self.config.get('base_url', 'http://localhost:11434')
        self.model = self.config.get('model', 'llama3.1:8b')
        self.temperature = self.config.get('temperature', 0.1)
        self.max_tokens = self.config.get('max_tokens', 4000)
        self.timeout = self.config.get('timeout', 120)
    
    def is_available(self) -> bool:
        """
        Vérifie si Ollama est disponible.
        
        Returns:
            bool: True si Ollama répond, False sinon
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False
    
    def list_models(self) -> list:
        """
        Liste les modèles disponibles dans Ollama.
        
        Returns:
            list: Liste des modèles disponibles
            
        Raises:
            OllamaError: Si impossible de récupérer la liste
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()
            data = response.json()
            return [model['name'] for model in data.get('models', [])]
        except requests.RequestException as e:
            raise OllamaError(f"Impossible de lister les modèles: {e}")
    
    def is_model_available(self, model_name: str) -> bool:
        """
        Vérifie si un modèle spécifique est disponible.
        
        Args:
            model_name: Nom du modèle à vérifier
            
        Returns:
            bool: True si le modèle est disponible
        """
        try:
            models = self.list_models()
            return model_name in models
        except OllamaError:
            return False
    
    def generate_response(self, prompt: str, model: Optional[str] = None) -> str:
        """
        Génère une réponse avec Ollama.
        
        Args:
            prompt: Le prompt à envoyer
            model: Modèle à utiliser (optionnel, utilise celui de la config)
            
        Returns:
            str: Réponse générée par le modèle
            
        Raises:
            OllamaError: Si erreur lors de la génération
        """
        model_to_use = model or self.model
        
        # Vérifier que le modèle est disponible
        if not self.is_model_available(model_to_use):
            available_models = self.list_models()
            raise OllamaError(
                f"Modèle '{model_to_use}' non disponible. "
                f"Modèles disponibles: {', '.join(available_models)}"
            )
        
        payload = {
            "model": model_to_use,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.temperature,
                "num_predict": self.max_tokens
            }
        }
        
        return self._execute_with_retry(
            lambda: self._generate_response_internal(payload),
            max_retries=3,
            operation_name="generate_response"
        )

    def _execute_with_retry(self, operation, max_retries: int = 3, operation_name: str = "operation"):
        """Exécute une opération avec retry logic robuste."""
        import time
        import random
        import requests

        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # Délai exponentiel avec jitter
                    delay = min(2 ** attempt + random.uniform(0, 1), 30)
                    print(f"⏳ Tentative {attempt + 1}/{max_retries + 1} dans {delay:.1f}s...")
                    time.sleep(delay)

                return operation()

            except requests.exceptions.Timeout as e:
                last_exception = e
                print(f"⚠️ Timeout lors de {operation_name} (tentative {attempt + 1})")
                if attempt == max_retries:
                    raise OllamaError(f"Timeout définitif après {max_retries + 1} tentatives")
                continue

            except requests.exceptions.ConnectionError as e:
                last_exception = e
                print(f"⚠️ Erreur de connexion lors de {operation_name} (tentative {attempt + 1})")
                if attempt == max_retries:
                    raise OllamaError(f"Erreur de connexion définitive: {e}")
                continue

            except requests.exceptions.HTTPError as e:
                last_exception = e
                if hasattr(e, 'response') and e.response is not None:
                    status_code = e.response.status_code
                    # Erreurs 5xx: retry, erreurs 4xx: pas de retry
                    if status_code >= 500:
                        print(f"⚠️ Erreur HTTP {status_code} lors de {operation_name} (tentative {attempt + 1})")
                        if attempt == max_retries:
                            raise OllamaError(f"Erreur HTTP {status_code} définitive")
                        continue
                    else:
                        # Erreur 4xx non récupérable
                        raise OllamaError(f"Erreur HTTP {status_code}: {e.response.text}")
                else:
                    raise OllamaError(f"Erreur HTTP: {e}")

            except OllamaError:
                # Les erreurs Ollama sont déjà formatées, les relancer directement
                raise

            except Exception as e:
                last_exception = e
                print(f"⚠️ Erreur inattendue lors de {operation_name}: {e}")
                if attempt == max_retries:
                    raise OllamaError(f"Erreur inattendue définitive: {e}")
                continue

        # Ne devrait jamais arriver
        raise OllamaError(f"Échec définitif de {operation_name}: {last_exception}")

    def _generate_response_internal(self, payload):
        """Implémentation interne de la génération de réponse."""
        import requests
        import json

        # Activer le streaming pour Ollama
        payload["stream"] = True

        response = requests.post(
            f"{self.base_url}/api/generate",
            json=payload,
            timeout=self.timeout,
            stream=True
        )
        response.raise_for_status()

        # Gérer le streaming Ollama
        return self._handle_ollama_streaming(response)

    def _handle_ollama_streaming(self, response):
        """Gère le streaming spécifique à Ollama."""
        import json
        import time

        full_response = ""
        chunk_count = 0
        last_chunk_time = time.time()

        print("🔄 Réception en cours", end="", flush=True)

        try:
            buffer = ""

            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    buffer += chunk
                    last_chunk_time = time.time()

                    # Traiter les lignes complètes dans le buffer
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()

                        if line:
                            try:
                                chunk_data = json.loads(line)

                                # Ollama envoie le texte dans le champ 'response'
                                if 'response' in chunk_data:
                                    response_text = chunk_data['response']
                                    if response_text:
                                        full_response += response_text
                                        chunk_count += 1

                                        if chunk_count % 10 == 0:
                                            print(".", end="", flush=True)

                                # Vérifier si c'est terminé
                                if chunk_data.get('done', False):
                                    print(" [DONE]", end="", flush=True)
                                    break

                            except json.JSONDecodeError:
                                # Ignorer les chunks JSON invalides
                                continue
                            except Exception:
                                # Ignorer les erreurs de chunk
                                continue

                # Vérifier timeout
                current_time = time.time()
                if current_time - last_chunk_time > 30:  # 30s sans nouveau chunk
                    print(" [Timeout stream]", end="", flush=True)
                    break

            # Traiter le buffer restant
            if buffer.strip():
                try:
                    chunk_data = json.loads(buffer.strip())
                    if 'response' in chunk_data and chunk_data['response']:
                        full_response += chunk_data['response']
                        chunk_count += 1
                except:
                    pass

            print(f"\n✅ Streaming terminé - {chunk_count} chunks reçus")
            print(f"📝 Réponse: {len(full_response)} caractères")

            return full_response.strip()

        except Exception as e:
            print(f"\n❌ Erreur streaming: {e}")
            # Fallback: essayer de lire la réponse complète
            try:
                if hasattr(response, 'raw'):
                    response.raw.seek(0)

                response_text = response.text
                if response_text:
                    # Essayer de parser chaque ligne comme JSON
                    lines = response_text.strip().split('\n')
                    fallback_response = ""
                    for line in lines:
                        if line.strip():
                            try:
                                data = json.loads(line)
                                if 'response' in data and data['response']:
                                    fallback_response += data['response']
                            except:
                                continue

                    if fallback_response:
                        print("🔄 Fallback: réponse récupérée en mode non-streaming")
                        return fallback_response.strip()
            except:
                pass

            raise OllamaError(f"Erreur lors du streaming: {e}")
    
    def chat_completion(self, messages: list, model: Optional[str] = None) -> str:
        """
        Génère une réponse en mode chat.
        
        Args:
            messages: Liste des messages (format OpenAI-like)
            model: Modèle à utiliser (optionnel)
            
        Returns:
            str: Réponse du modèle
            
        Raises:
            OllamaError: Si erreur lors de la génération
        """
        model_to_use = model or self.model
        
        # Convertir les messages en prompt simple pour Ollama
        prompt_parts = []
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        prompt = "\n\n".join(prompt_parts) + "\n\nAssistant:"
        
        return self.generate_response(prompt, model_to_use)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Teste la connexion à Ollama et retourne des informations.
        
        Returns:
            dict: Informations sur la connexion et les modèles
        """
        result = {
            'available': False,
            'models': [],
            'current_model': self.model,
            'error': None
        }
        
        try:
            if not self.is_available():
                result['error'] = "Ollama n'est pas accessible"
                return result
            
            result['available'] = True
            result['models'] = self.list_models()
            
            # Tester le modèle actuel
            if self.model not in result['models']:
                result['error'] = f"Modèle '{self.model}' non disponible"
            
        except Exception as e:
            result['error'] = str(e)
        
        return result


def create_ollama_client(model: str = None, base_url: str = None) -> OllamaClient:
    """
    Crée un client Ollama avec une configuration personnalisée.
    
    Args:
        model: Nom du modèle à utiliser
        base_url: URL de base d'Ollama
        
    Returns:
        OllamaClient: Instance configurée du client
    """
    config = DefaultConfigurations.OLLAMA_CONFIG.copy()
    
    if model:
        config['model'] = model
    if base_url:
        config['base_url'] = base_url
    
    return OllamaClient(config)