#!/usr/bin/env python3
"""
Orchestrateur principal pour l'analyse automatisée ARC AGI.

Ce module coordonne tout le workflow d'analyse automatisée, de la génération
du prompt à la validation de la solution, avec intégration optionnelle
du système d'apprentissage.
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))
# Forcer l'encodage UTF-8 pour la sortie
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Import des composants du système automatisé
from .secure_puzzle_loader import SecurePuzzleLoader
from .chat_session import ChatSession, SessionConfig
from .response_parser import ResponseParser
from .solution_validator import SolutionValidator
from .learning_system import LearningSystem
from .analysis_cache import AnalysisCache
from .data_models import AnalysisResult, ParsedResponse, ValidationResult
from .constants import (
    ExitCodes, AnalysisStatus, ChatSessionTypes, MessageTemplates,
    DefaultConfigurations, SystemLimits, AIProviders
)
from .exceptions import (
    PuzzleNotFoundError, ChatSessionError, ResponseParsingError,
    SolutionValidationError, LearningSystemError
)

# Import des composants existants
from .arc_analyzer import ARCAnalyzer
try:
    from utils.extract_key_insights import extract_key_insights, format_insights_for_prompt
except ImportError:
    # Fallback si les utilitaires ne sont pas disponibles
    def extract_key_insights(analysis_data):
        return {"critical_observations": []}
    def format_insights_for_prompt(insights):
        return ""


class AutomatedARCAnalyzer:
    """
    Orchestrateur principal pour l'analyse automatisée ARC AGI.
    
    Cette classe coordonne tout le workflow d'analyse automatisée:
    1. Chargement sécurisé du puzzle
    2. Génération du prompt amélioré
    3. Session chat avec l'IA
    4. Parsing de la réponse
    5. Validation de la solution
    6. Apprentissage optionnel
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialise l'analyseur automatisé.
        
        Args:
            config: Configuration optionnelle pour l'analyseur
        """
        self.config = config or {}
        self.logger = self._setup_logging()
        
        # Initialiser les composants
        self.puzzle_loader = SecurePuzzleLoader(
            arc_data_dir=self.config.get('arc_data_dir', '../arc-puzzle/arcdata')
        )
        self.arc_analyzer = ARCAnalyzer()
        self.response_parser = ResponseParser()
        self.solution_validator = SolutionValidator(
            config=self.config.get('validation_config', DefaultConfigurations.VALIDATION_CONFIG)
        )
        self.learning_system = LearningSystem()
        
        # Configuration des sessions chat
        self.chat_config = SessionConfig(
            session_type=self.config.get('chat_session_type', ChatSessionTypes.BLIND_ANALYSIS),
            timeout=self.config.get('chat_timeout', SystemLimits.CHAT_TIMEOUT_SECONDS),
            max_retries=self.config.get('max_retries', SystemLimits.MAX_RETRY_ATTEMPTS),
            ai_provider=self.config.get('ai_provider', AIProviders.SIMULATION),
            ollama_model=self.config.get('ollama_model'),
            ollama_base_url=self.config.get('ollama_base_url'),
            openai_model=self.config.get('openai_model'),
            openrouter_model=self.config.get('openrouter_model'),
            huggingface_model=self.config.get('huggingface_model')
        )
        
        # Options d'analyse
        self.enable_learning = self.config.get('enable_learning', True)
        self.save_success = self.config.get('save_success', True)
        self.show_progress = self.config.get('show_progress', True)
        
        # Répertoires de travail
        self.analysis_dir = Path(self.config.get('analysis_dir', 'analysis_data'))
        self.results_dir = Path(self.config.get('results_dir', 'arc_results'))
        
        # Créer les répertoires si nécessaire
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialiser le cache d'analyses
        self.analysis_cache = AnalysisCache(
            cache_dir=self.analysis_dir,
            max_age_days=self.config.get('cache_max_age_days', 30)
        )
    
    def _setup_logging(self) -> logging.Logger:
        """Configure le système de logging."""
        logger = logging.getLogger(__name__)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        log_level = self.config.get('log_level', 'INFO')
        logger.setLevel(getattr(logging, log_level))
        
        return logger
    
    def analyze_puzzle(self, puzzle_id: str, options: Optional[Dict[str, Any]] = None) -> AnalysisResult:
        """
        Workflow complet d'analyse automatisée pour un puzzle.
        
        Args:
            puzzle_id: ID du puzzle à analyser
            options: Options spécifiques pour cette analyse
            
        Returns:
            AnalysisResult avec tous les détails de l'analyse
            
        Raises:
            PuzzleNotFoundError: Si le puzzle n'est pas trouvé
            ChatSessionError: Si la session chat échoue
            ResponseParsingError: Si le parsing de la réponse échoue
            SolutionValidationError: Si la validation échoue
        """
        start_time = time.time()
        analysis_options = options or {}
        
        try:
            if self.show_progress:
                print(MessageTemplates.ANALYSIS_START.format(puzzle_id=puzzle_id))
            
            self.logger.info(f"Début de l'analyse automatisée pour {puzzle_id}")
            
            # 1. Chargement sécurisé du puzzle
            puzzle_data, hidden_solution = self._load_puzzle_securely(puzzle_id)
            
            # 2. Génération du prompt amélioré
            enhanced_prompt = self._generate_enhanced_prompt(puzzle_data)
            
            # 3. Session chat avec l'IA
            ai_response = self._conduct_chat_session(enhanced_prompt, puzzle_id)
            
            # 4. Parsing de la réponse IA
            parsed_response = self._parse_ai_response(ai_response)
            
            # 5. Validation de la solution
            validation_result = self._validate_solution(
                parsed_response.proposed_grid, hidden_solution, puzzle_id
            )
            
            # 6. Création du résultat d'analyse
            execution_time = time.time() - start_time
            analysis_result = AnalysisResult(
                puzzle_id=puzzle_id,
                success=validation_result.is_correct if validation_result else False,
                proposed_solution=parsed_response.proposed_grid,
                validation_result=validation_result,
                ai_analysis=parsed_response,
                execution_time=execution_time,
                saved_to=None,
                timestamp=datetime.now()
            )
            
            # 7. Sauvegarde si succès
            if analysis_result.success and self.save_success:
                saved_path = self.save_successful_analysis(analysis_result, puzzle_data)
                analysis_result.saved_to = saved_path
            
            # 8. Apprentissage optionnel
            if analysis_result.success and self.enable_learning:
                self._conduct_learning_session(analysis_result, puzzle_data)
            
            # 9. Affichage du résultat
            self._display_analysis_result(analysis_result)
            
            self.logger.info(f"Analyse terminée pour {puzzle_id} en {execution_time:.2f}s")
            return analysis_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_result = AnalysisResult(
                puzzle_id=puzzle_id,
                success=False,
                proposed_solution=None,
                validation_result=None,
                ai_analysis=None,
                execution_time=execution_time,
                saved_to=None,
                timestamp=datetime.now(),
                error_message=str(e)
            )
            
            self.logger.error(f"Erreur lors de l'analyse de {puzzle_id}: {e}")
            if self.show_progress:
                print(f"❌ Erreur: {e}")
            
            return error_result
    
    def _load_puzzle_securely(self, puzzle_id: str) -> tuple:
        """Charge le puzzle en cachant la solution."""
        if self.show_progress:
            print(MessageTemplates.LOADING_PUZZLE.format(puzzle_id=puzzle_id))
        
        try:
            puzzle_data, hidden_solution = self.puzzle_loader.load_puzzle_for_analysis(puzzle_id)
            self.logger.debug(f"Puzzle {puzzle_id} chargé depuis {puzzle_data.subset}")
            return puzzle_data, hidden_solution
            
        except Exception as e:
            raise PuzzleNotFoundError(puzzle_id, []) from e
    
    def _generate_enhanced_prompt(self, puzzle_data) -> str:
        """Génère un prompt amélioré avec insights."""
        if self.show_progress:
            print(MessageTemplates.GENERATING_PROMPT)
        
        try:
            # 1. Générer l'analyse de base si nécessaire (avec cache)
            analysis_data = self._ensure_analysis_exists(puzzle_data)
            
            # 2. Extraire les insights
            insights = extract_key_insights(analysis_data)
            
            # 3. Créer le prompt amélioré
            enhanced_prompt = self._build_enhanced_prompt(puzzle_data, insights)
            
            self.logger.debug(f"Prompt généré avec {len(insights.get('critical_observations', []))} insights")
            return enhanced_prompt
            
        except Exception as e:
            self.logger.error(f"❌ Erreur lors de la génération du prompt: {e}")
            # Fallback: prompt basique sans insights
            return self._build_basic_prompt(puzzle_data)
    
    def _ensure_analysis_exists(self, puzzle_data) -> Dict[str, Any]:
        """S'assure que l'analyse existe, la génère si nécessaire avec cache."""
        # Vérifier d'abord le cache
        cached_analysis = self.analysis_cache.load_cached_analysis(
            puzzle_data.puzzle_id, puzzle_data.subset
        )
        
        if cached_analysis:
            self.logger.debug(f"Analyse trouvée en cache pour {puzzle_data.puzzle_id}")
            return cached_analysis
        
        # Générer l'analyse si pas en cache
        self.logger.info(f"Génération de l'analyse pour {puzzle_data.puzzle_id}")
        
        # Reconstituer le puzzle complet pour l'analyse
        puzzle_dict = {
            'train': puzzle_data.train_examples,
            'test': [{'input': puzzle_data.test_input.tolist()}]
        }
        
        # Générer l'analyse
        analysis = self.arc_analyzer.analyze_puzzle(puzzle_dict)
        
        # Ajouter les métadonnées
        analysis_data = {
            'puzzle_id': puzzle_data.puzzle_id,
            'subset': puzzle_data.subset,
            'raw_analysis': analysis
        }
        
        # Sauvegarder dans le cache
        self.analysis_cache.save_analysis_to_cache(
            puzzle_data.puzzle_id, puzzle_data.subset, analysis_data
        )
        
        return analysis_data
    
    def _build_enhanced_prompt(self, puzzle_data, insights: Dict[str, Any]) -> str:
        """Construit un prompt amélioré avec insights."""
        prompt_parts = []
        
        # En-tête
        prompt_parts.append("# Analyse de Puzzle ARC AGI")
        prompt_parts.append(f"Puzzle ID: {puzzle_data.puzzle_id}")
        prompt_parts.append("🇫🇷 IMPORTANT : Réponds UNIQUEMENT en français 🇫🇷")
        prompt_parts.append("")
        
        # Contexte spatial crucial pour la compréhension
        prompt_parts.append("## Contexte Spatial et Conceptuel")
        prompt_parts.append("Tu es un expert en analyse ARC AGI.")
        prompt_parts.append("Visualiser les valeurs de couleurs comme des objets 3D en couleurs sur une table et que tu regardais d'en haut.")
        prompt_parts.append("Tu peux déplacer les cubes, en rajouter, en enlever, les prolonger, les grossir, les rappetissir, les multiplier, les diviser,")
        prompt_parts.append("les tourner, leur faire une action miroir, les changer de couleurs, etc.")
        prompt_parts.append("Tu dois aussi penser Motif en associant des cubes de même valeurs qui forment un ensemble manipulables.")
        prompt_parts.append("")
        prompt_parts.append("Chaque nombre représente une couleur de cube :")
        prompt_parts.append("Mapping des couleurs pour l'affichage:")
        prompt_parts.append("```text")
        prompt_parts.append("0: '▪️' (fond/vide)")
        prompt_parts.append("1: '🟦' (bleu)")
        prompt_parts.append("2: '🟥' (rouge)")
        prompt_parts.append("3: '🟩' (vert)")
        prompt_parts.append("4: '🟨' (jaune)")
        prompt_parts.append("5: '🔳' (gris/noir)")
        prompt_parts.append("6: '🟪' (magenta/violet)")
        prompt_parts.append("7: '🟧' (orange)")
        prompt_parts.append("8: '⬜' (cyan/blanc)")
        prompt_parts.append("9: '🟫' (marron)")
        prompt_parts.append("```")

        ### 1. **Ajouter des exemples de transformations physiques**
        prompt_parts.append("```text")
        prompt_parts.append("Exemples de manipulations possibles :")
        prompt_parts.append("- COPIER un motif et le COLLER ailleurs")
        prompt_parts.append("- ÉTIRER un motif (x2, x3)")
        prompt_parts.append("- RELIER deux objets par un pont")
        prompt_parts.append("- COMPLÉTER un motif partiel")
        prompt_parts.append("- SUPERPOSER des couches")
        prompt_parts.append("```")

        ### 2. **Structure d'analyse progressive**
        prompt_parts.append("```text")
        prompt_parts.append("## Méthode d'analyse recommandée :")
        prompt_parts.append("1. Identifier les OBJETS distincts (groupes de couleurs)")
        prompt_parts.append("2. Repérer le MOTIF DE RÉFÉRENCE (généralement le plus complexe)")
        prompt_parts.append("3. Identifier les OBJETS INCOMPLETS ou ISOLÉS")
        prompt_parts.append("4. Déterminer l'ACTION DE TRANSFORMATION (copier, étendre, relier...)")
        prompt_parts.append("5. Valider sur TOUS les exemples avant de proposer")
        prompt_parts.append("```")

    ### 3. **Vocabulaire d'actions physiques**
        prompt_parts.append("```text")
        prompt_parts.append("Actions courantes en ARC :")
        prompt_parts.append("- DUPLIQUER : copier un motif ailleurs")
        prompt_parts.append("- ÉTENDRE : agrandir un motif existant")
        prompt_parts.append("- RELIER : connecter des objets séparés")
        prompt_parts.append("- COMPLÉTER : ajouter les parties manquantes")
        prompt_parts.append("- ADAPTER : modifier la taille selon le contexte")
        prompt_parts.append("```")

        ### 4. **Test de cohérence obligatoire**
        prompt_parts.append("```text")
        prompt_parts.append("⚠️ VALIDATION OBLIGATOIRE :")
        prompt_parts.append("Avant toute proposition de solution :")
        prompt_parts.append("- Appliquer la règle sur l'Exemple 1 → Vérifier l'output exact")
        prompt_parts.append("- Appliquer la règle sur l'Exemple 2 → Vérifier l'output exact")
        prompt_parts.append("- Appliquer la règle sur l'Exemple 3 → Vérifier l'output exact")
        prompt_parts.append("- Si une seule vérification échoue → Rejeter la règle")
        prompt_parts.append("```")

        prompt_parts.append("Pense en termes de manipulations spatiales 3D vues du dessus :")
        prompt_parts.append("- Rotation (90°, 180°, 270°)")
        prompt_parts.append("- Miroir (horizontal, vertical)")
        prompt_parts.append("- Translation (déplacement)")
        prompt_parts.append("- Ajout/suppression de cubes")
        prompt_parts.append("- Changement de couleurs")
        prompt_parts.append("- Formation de motifs ou patterns")
        prompt_parts.append("- Règles conditionnelles (si X alors Y)")
        prompt_parts.append("")

        prompt_parts.append("## Contexte Spatial et Conceptuel"
        "")
        
        # Insights extraits
        if insights.get('critical_observations'):
            prompt_parts.append("## Insights Automatiques")
            for obs in insights['critical_observations']:
                prompt_parts.append(f"• {obs}")
            prompt_parts.append("")
        
        # Exemples d'entraînement
        prompt_parts.append("## Exemples d'Entraînement")
        for i, example in enumerate(puzzle_data.train_examples):
            prompt_parts.append(f"### Exemple {i+1}")
            prompt_parts.append("**Input:**")
            prompt_parts.append("```text")
            # prompt_parts.append(self._format_grid(example['input']))
            prompt_parts.append(self._format_grid_visual(example['input']))
            prompt_parts.append("```")
            prompt_parts.append("**Output:**")
            prompt_parts.append("```text")
            # prompt_parts.append(self._format_grid(example['output']))
            prompt_parts.append(self._format_grid_visual(example['output']))
            prompt_parts.append("```")
            prompt_parts.append("")
        
        # Grille test
        prompt_parts.append("## Grille Test")
        prompt_parts.append("**Input:**")
        prompt_parts.append("```text")
        #prompt_parts.append(self._format_grid(puzzle_data.test_input.tolist()))
        prompt_parts.append(self._format_grid_visual(puzzle_data.test_input.tolist()))
        prompt_parts.append("```")
        prompt_parts.append("")
        
        # Instructions détaillées
        prompt_parts.append("## Instructions d'Analyse")
        prompt_parts.append("🇫🇷 IMPORTANT : Réponds UNIQUEMENT en français - aucun mot anglais autorisé ! 🇫🇷")
        prompt_parts.append("")
        prompt_parts.append("En gardant à l'esprit la visualisation des cubes colorés, analysez les exemples pour identifier :")
        prompt_parts.append("")
        prompt_parts.append("1. **Transformations spatiales** : rotations, miroirs, déplacements")
        prompt_parts.append("2. **Manipulations de cubes** : ajout, suppression, changement de couleur")
        prompt_parts.append("3. **Patterns et motifs** : formes récurrentes, structures géométriques")
        prompt_parts.append("4. **Règles conditionnelles** : si un cube est à telle position/couleur, alors...")
        prompt_parts.append("5. **Relations spatiales** : proximité, alignement, symétrie")
        prompt_parts.append("")
        prompt_parts.append("**Méthodologie :**")
        prompt_parts.append("- Comparez chaque paire input/output comme des arrangements de cubes")
        prompt_parts.append("- Identifiez les cubes qui bougent, changent ou disparaissent")
        prompt_parts.append("- Cherchez les invariants (ce qui ne change jamais)")
        prompt_parts.append("- Formulez des hypothèses sur les règles de transformation")
        prompt_parts.append("- Testez vos hypothèses sur tous les exemples")
        prompt_parts.append("- Appliquez la règle validée à la grille test")
        prompt_parts.append("")
        prompt_parts.append("**Format de réponse attendu :**")
        prompt_parts.append("Fournissez votre raisonnement détaillé et la grille output finale au format JSON.")
        
        return "\n".join(prompt_parts)
    
    def _build_basic_prompt(self, puzzle_data) -> str:
        """Construit un prompt basique sans insights."""
        prompt_parts = []
        
        prompt_parts.append("# Analyse de Puzzle ARC AGI")
        prompt_parts.append(f"Puzzle ID: {puzzle_data.puzzle_id}")
        prompt_parts.append("")
        
        # Exemples d'entraînement
        prompt_parts.append("## Exemples d'Entraînement")
        for i, example in enumerate(puzzle_data.train_examples):
            prompt_parts.append(f"### Exemple {i+1}")
            prompt_parts.append("**Input:**")
            prompt_parts.append(self._format_grid(example['input']))
            prompt_parts.append("**Output:**")
            prompt_parts.append(self._format_grid(example['output']))
            prompt_parts.append("")
        
        # Grille test
        prompt_parts.append("## Grille Test")
        prompt_parts.append("**Input:**")
        prompt_parts.append("```text")
        prompt_parts.append(self._format_grid(puzzle_data.test_input.tolist()))
        prompt_parts.append("```")
        prompt_parts.append("")
        
        # Instructions
        prompt_parts.append("## Instructions")
        prompt_parts.append("Analysez les exemples pour identifier les transformations et générez la solution.")
        
        return "\n".join(prompt_parts)
    
    def _format_grid(self, grid) -> str:
        """Formate une grille pour l'affichage."""
        if isinstance(grid, list):
            return "\n".join([str(row) for row in grid])
        else:
            return "\n".join([str(row.tolist()) for row in grid])
    
    def _conduct_chat_session(self, prompt: str, puzzle_id: str) -> str:
        """Conduit une session chat avec l'IA."""
        if self.show_progress:
            print(MessageTemplates.CHAT_SESSION)
        
        try:
            self.logger.debug(f"Création de la session chat avec provider: {self.chat_config.ai_provider}")
            with ChatSession(self.chat_config) as session:
                self.logger.debug(f"Envoi du prompt ({len(prompt)} caractères) à {self.chat_config.ai_provider}")
                response = session.send_prompt(prompt)
                
                self.logger.debug(f"Réponse reçue pour {puzzle_id}: {len(response)} caractères")
                return response
                
        except Exception as e:
            raise ChatSessionError(f"Échec de la session chat: {e}") from e
    
    def _parse_ai_response(self, response: str) -> ParsedResponse:
        """Parse la réponse de l'IA."""
        if self.show_progress:
            print(MessageTemplates.PARSING_RESPONSE)
        
        try:
            parsed = self.response_parser.parse_ai_response(response)
            
            self.logger.debug(f"Réponse parsée: {len(parsed.transformations)} transformations, "
                            f"{len(parsed.patterns)} patterns, {len(parsed.rules)} règles")
            
            return parsed
            
        except Exception as e:
            raise ResponseParsingError(f"Échec du parsing: {e}") from e
    
    def _validate_solution(self, proposed_grid, hidden_solution, puzzle_id: str) -> Optional[ValidationResult]:
        """Valide la solution proposée."""
        if self.show_progress:
            print(MessageTemplates.VALIDATING_SOLUTION)
        
        if proposed_grid is None:
            self.logger.warning(f"Aucune grille proposée pour {puzzle_id}")
            return None
        
        if hidden_solution is None:
            self.logger.warning(f"Aucune solution cachée disponible pour {puzzle_id}")
            return None
        
        try:
            validation = self.solution_validator.validate_solution(proposed_grid, hidden_solution)
            
            self.logger.info(f"Validation {puzzle_id}: {validation.accuracy_percentage:.1f}% précision")
            
            return validation
            
        except Exception as e:
            raise SolutionValidationError(f"Échec de la validation: {e}") from e
    
    def _conduct_learning_session(self, analysis_result: AnalysisResult, puzzle_data) -> None:
        """Conduit une session d'apprentissage optionnelle."""
        if self.show_progress:
            print(MessageTemplates.EXTRACTING_INSIGHTS)
        
        try:
            # Demander à l'utilisateur s'il veut extraire des insights
            if self.learning_system.propose_learning(analysis_result):
                # Préparer les données d'analyse complètes
                successful_analysis = {
                    "puzzle_id": analysis_result.puzzle_id,
                    "analysis_data": {
                        "raw_analysis": {
                            "transformations": {"color": {"color_removal": True}},
                            "diff_analysis": {
                                "common_color_changes": {},
                                "train_diffs": [{"change_percentage": 5.0}]
                            }
                        }
                    }
                }
                
                # Extraire et intégrer les insights
                self.learning_system.extract_and_integrate_insights(successful_analysis)
                
        except Exception as e:
            self.logger.error(f"⚠️ Erreur lors de l'apprentissage: {e}")
            if self.show_progress:
                print(f"⚠️ Erreur d'apprentissage: {e}")
    
    def _display_analysis_result(self, result: AnalysisResult) -> None:
        """Affiche le résultat de l'analyse."""
        if not self.show_progress:
            return
        
        print("\n" + "="*60)
        print(f"📊 RÉSULTAT D'ANALYSE - {result.puzzle_id}")
        print("="*60)
        
        if result.success:
            print(MessageTemplates.ANALYSIS_SUCCESS.format(puzzle_id=result.puzzle_id))
            if result.validation_result:
                print(f"✅ Précision: {result.validation_result.accuracy_percentage:.1f}%")
        else:
            print(MessageTemplates.ANALYSIS_FAILURE.format(puzzle_id=result.puzzle_id))
            if result.error_message:
                print(f"❌ Erreur: {result.error_message}")
        
        print(f"⏱️  Temps d'exécution: {result.execution_time:.2f}s")
        
        if result.saved_to:
            print(f"💾 Sauvegardé dans: {result.saved_to}")
        
        print("="*60)
    
    def save_successful_analysis(self, analysis_result: AnalysisResult, puzzle_data) -> str:
        """
        Sauvegarde une analyse réussie dans le format structuré spécifié.
        
        Args:
            analysis_result: Résultat d'analyse réussie
            puzzle_data: Données du puzzle analysé
            
        Returns:
            str: Chemin du fichier sauvegardé
        """
        if self.show_progress:
            print(MessageTemplates.SAVING_RESULTS)
        
        try:
            # Créer le nom de fichier
            filename = f"puzzle_{analysis_result.puzzle_id}_success.txt"
            filepath = self.results_dir / filename
            
            # Créer le contenu structuré
            content = self._format_successful_analysis(analysis_result, puzzle_data)
            
            # Sauvegarder
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"Analyse sauvegardée: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            raise
    
    def _format_successful_analysis(self, result: AnalysisResult, puzzle_data) -> str:
        """
        Formate l'analyse réussie selon le format structuré spécifié.
        
        Args:
            result: Résultat d'analyse
            puzzle_data: Données du puzzle
            
        Returns:
            str: Contenu formaté pour la sauvegarde
        """
        lines = []
        
        # En-tête
        timestamp = result.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        lines.append(f"=== ANALYSE PUZZLE {result.puzzle_id} - {timestamp} ===")
        lines.append("")
        
        # Métadonnées
        lines.append("📊 MÉTADONNÉES")
        lines.append(f"Puzzle ID: {result.puzzle_id}")
        lines.append(f"Subset: {puzzle_data.subset}")
        lines.append(f"Durée d'analyse: {result.execution_time:.1f}s")
        lines.append(f"Statut: {'SUCCÈS' if result.success else 'ÉCHEC'}")
        lines.append("")
        
        # Transformations détectées
        if result.ai_analysis and result.ai_analysis.transformations:
            lines.append("🔍 TRANSFORMATIONS DÉTECTÉES")
            for transformation in result.ai_analysis.transformations:
                lines.append(f"- {transformation}")
            lines.append("")
        
        # Patterns identifiés
        if result.ai_analysis and result.ai_analysis.patterns:
            lines.append("🎯 PATTERNS IDENTIFIÉS")
            for pattern in result.ai_analysis.patterns:
                lines.append(f"- {pattern}")
            lines.append("")
        
        # Règles déduites
        if result.ai_analysis and result.ai_analysis.rules:
            lines.append("📋 RÈGLES DÉDUITES")
            for rule in result.ai_analysis.rules:
                lines.append(f"- {rule}")
            lines.append("")
        
        # Interprétation grille test
        if result.ai_analysis and result.ai_analysis.interpretation:
            lines.append("💡 INTERPRÉTATION GRILLE TEST")
            lines.append(result.ai_analysis.interpretation)
            lines.append("")
        
        # Grille output proposée
        if result.proposed_solution is not None:
            lines.append("🎲 GRILLE OUTPUT PROPOSÉE")
            lines.append(self._format_grid_visual(result.proposed_solution))
            lines.append("")
        
        # Raisonnement étape par étape
        if result.ai_analysis and result.ai_analysis.reasoning_steps:
            lines.append("🧠 RAISONNEMENT ÉTAPE PAR ÉTAPE")
            for i, step in enumerate(result.ai_analysis.reasoning_steps, 1):
                lines.append(f"{i}. {step}")
            lines.append("")
        
        # Validation
        lines.append("✅ VALIDATION")
        if result.validation_result:
            if result.validation_result.is_correct:
                lines.append("SUCCÈS - Puzzle résolu !")
            else:
                lines.append("ÉCHEC - Solution incorrecte")
                lines.append(f"Précision: {result.validation_result.accuracy_percentage:.1f}%")
                lines.append(f"Erreurs: {result.validation_result.total_errors}")
                
                # Grille de diagnostic si disponible
                if result.validation_result.diagnostic_grid is not None:
                    lines.append("")
                    lines.append("Grille de diagnostic (T=correct, F=incorrect):")
                    lines.append(self._format_diagnostic_grid(result.validation_result.diagnostic_grid))
        else:
            lines.append("Validation non disponible")
        lines.append("")
        
        # Pied de page
        lines.append("=== FIN ANALYSE ===")
        
        return "\n".join(lines)
    
    def _format_grid_visual(self, grid) -> str:
        """Formate une grille pour l'affichage visuel."""
        if grid is None:
            return "Grille non disponible"
        
        # Mapping des couleurs pour l'affichage
        color_symbols = { 
            0: "▪️", # Fond/vide
            1: "🟦", # Bleu
            2: "🟥", # Rouge
            3: "🟩", # Vert
            4: "🟨", # Jaune
            5: "🔳", # Gris/Noir
            6: "🟪", # Magenta/Violet
            7: "🟧", # Orange
            8: "⬜", # Cyan/Blanc
            9: "🟫", # Marron
        }        
        
        lines = []
        for row in grid:
            visual_row = ''.join(color_symbols.get(int(cell), '❓') for cell in row)
            numeric_row = '[' + ','.join(str(int(cell)) for cell in row) + ']'
            lines.append(f"{visual_row}\t\t\t\t\t\t{numeric_row}")
        
        return '\n'.join(lines)
    
    def _format_diagnostic_grid(self, diagnostic_grid) -> str:
        """Formate la grille de diagnostic."""
        lines = []
        for row in diagnostic_grid:
            line = ' '.join(str(cell) for cell in row)
            lines.append(line)
        return '\n'.join(lines)
    
    def cleanup_cache(self) -> int:
        """
        Nettoie les analyses obsolètes du cache.
        
        Returns:
            int: Nombre d'analyses supprimées
        """
        return self.analysis_cache.cleanup_obsolete_analyses()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Retourne les statistiques du cache.
        
        Returns:
            Dict contenant les statistiques du cache
        """
        return self.analysis_cache.get_cache_stats()
    
    def preload_analyses(self, puzzle_ids: list, subset: str) -> Dict[str, Dict[str, Any]]:
        """
        Précharge plusieurs analyses pour optimiser les performances.
        
        Args:
            puzzle_ids: Liste des IDs de puzzles à précharger
            subset: Subset (training/evaluation)
            
        Returns:
            Dict mapping puzzle_id -> analysis_data
        """
        return self.analysis_cache.preload_analyses(puzzle_ids, subset)
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """
        Retourne un résumé des analyses effectuées.
        
        Returns:
            Dict contenant les statistiques d'analyse
        """
        cache_stats = self.get_cache_stats()
        
        return {
            "analyzer_version": "1.0",
            "components": {
                "puzzle_loader": "SecurePuzzleLoader",
                "chat_session": "ChatSession",
                "response_parser": "ResponseParser",
                "solution_validator": "SolutionValidator",
                "learning_system": "LearningSystem",
                "analysis_cache": "AnalysisCache"
            },
            "configuration": {
                "chat_session_type": self.chat_config.session_type,
                "enable_learning": self.enable_learning,
                "save_success": self.save_success,
                "cache_max_age_days": self.analysis_cache.max_age_days
            },
            "cache_statistics": cache_stats
        }